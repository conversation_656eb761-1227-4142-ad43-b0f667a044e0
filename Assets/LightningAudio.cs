using UnityEngine;
using FMODUnity;
using FMOD.Studio;
using System.Collections.Generic;

/// <summary>
/// Lightning audio system that plays unique violin notes for each lightning bolt particle.
/// Integrates with the existing AudioManager LOD system for performance optimization.
/// </summary>
[RequireComponent(typeof(ParticleSystem))]
public class LightningAudio : MonoBehaviour
{
    [Header("FMOD Audio Settings")]
    [Tooltip("FMOD event for violin lightning notes")]
    [SerializeField] private EventReference violinLightningEvent;

    [Header("Audio Behavior")]
    [Tooltip("Minimum time between lightning audio events (prevents audio spam)")]
    [SerializeField] private float minTimeBetweenEvents = 0.05f;

    [Tooltip("Maximum number of simultaneous lightning sounds (local throttling)")]
    [SerializeField] private int maxSimultaneousLightningSounds = 50;

    [Tooltip("Probability that a lightning bolt will play audio (0-1)")]
    [Range(0f, 1f)]
    [SerializeField] private float audioPlayProbability = 0.8f;

    [Head<PERSON>("Violin Note Variation")]
    [Tooltip("Minimum pitch multiplier for violin notes")]
    [SerializeField] private float minPitch = 0.5f;

    [Tooltip("Maximum pitch multiplier for violin notes")]
    [SerializeField] private float maxPitch = 2.0f;

    [Tooltip("Use discrete musical notes instead of random pitch")]
    [SerializeField] private bool useMusicalNotes = true;

    [Header("Performance Settings")]
    [Tooltip("How often to check for new particles (seconds)")]
    [SerializeField] private float particleCheckInterval = 0.02f;

    [Tooltip("Maximum distance from player to play lightning audio")]
    [SerializeField] private float maxAudioDistance = 100f;

    // Component references
    private ParticleSystem lightningParticleSystem;
    private Transform playerTransform;

    // Particle tracking
    private ParticleSystem.Particle[] particlesArray;
    private HashSet<uint> processedParticles = new HashSet<uint>();
    private Queue<float> recentAudioTimes = new Queue<float>();

    // Audio instance tracking for traveling sounds
    private Dictionary<uint, LightningAudioInstance> activeAudioInstances = new Dictionary<uint, LightningAudioInstance>();

    // Timing
    private float lastParticleCheckTime;
    private float lastAudioEventTime;

    // Helper class to track audio instances with their particles
    private class LightningAudioInstance
    {
        public EventInstance eventInstance;
        public uint particleId;
        public float startTime;
        public bool isValid;

        public LightningAudioInstance(EventInstance instance, uint id)
        {
            eventInstance = instance;
            particleId = id;
            startTime = Time.time;
            isValid = true;
        }

        public void UpdatePosition(Vector3 position)
        {
            if (isValid && eventInstance.isValid())
            {
                eventInstance.set3DAttributes(RuntimeUtils.To3DAttributes(position));
            }
        }

        public void Stop()
        {
            if (isValid && eventInstance.isValid())
            {
                eventInstance.stop(FMOD.Studio.STOP_MODE.ALLOWFADEOUT);
                eventInstance.release();
                isValid = false;
            }
        }
    }

    void Start()
    {
        InitializeComponents();
        InitializeParticleTracking();
    }

    void Update()
    {
        UpdateAudioThrottling();
        CheckForNewLightningBolts();
        UpdateTravelingAudioPositions();
    }

    private void InitializeComponents()
    {
        lightningParticleSystem = GetComponent<ParticleSystem>();
        if (lightningParticleSystem == null)
        {
            Debug.LogError($"LightningAudio: No ParticleSystem found on {gameObject.name}!");
            enabled = false;
            return;
        }

        // Get player reference from GameManager
        if (GameManager.Instance != null && GameManager.Instance.player != null)
        {
            playerTransform = GameManager.Instance.player.transform;
        }
        else if (Camera.main != null)
        {
            playerTransform = Camera.main.transform;
        }
        else
        {
            Debug.LogWarning("LightningAudio: No player or camera found for distance calculations!");
        }

        if (violinLightningEvent.IsNull)
        {
            Debug.LogWarning($"LightningAudio: No violin lightning event assigned on {gameObject.name}!");
        }
    }

    private void InitializeParticleTracking()
    {
        // Initialize particle array with max particles capacity
        int maxParticles = lightningParticleSystem.main.maxParticles;
        particlesArray = new ParticleSystem.Particle[maxParticles];

        Debug.Log($"LightningAudio: Initialized for up to {maxParticles} lightning particles on {gameObject.name}");
    }

    private void UpdateAudioThrottling()
    {
        float currentTime = Time.time;

        // Clean up old audio event times (assume 1 second duration for lightning sounds)
        while (recentAudioTimes.Count > 0 && currentTime - recentAudioTimes.Peek() > 1.0f)
        {
            recentAudioTimes.Dequeue();
        }

        // Note: We now use activeAudioInstances.Count for simultaneous sound tracking
    }

    private void CheckForNewLightningBolts()
    {
        float currentTime = Time.time;

        // Throttle particle checking for performance
        if (currentTime - lastParticleCheckTime < particleCheckInterval)
            return;

        lastParticleCheckTime = currentTime;

        // Skip if no FMOD event assigned
        if (violinLightningEvent.IsNull)
            return;

        // Get current particles
        int particleCount = lightningParticleSystem.GetParticles(particlesArray);
        if (particleCount == 0)
            return;

        // Check for new particles and play audio
        for (int i = 0; i < particleCount; i++)
        {
            ref ParticleSystem.Particle particle = ref particlesArray[i];
            uint particleId = particle.randomSeed;

            // Skip if we've already processed this particle
            if (processedParticles.Contains(particleId))
                continue;

            // Mark particle as processed
            processedParticles.Add(particleId);

            // Try to play audio for this new lightning bolt
            TryPlayLightningAudio(particleId, particle.position, currentTime);
        }

        // Clean up processed particles that no longer exist
        CleanupProcessedParticles(particleCount);
    }

    private void TryPlayLightningAudio(uint particleId, Vector3 position, float currentTime)
    {
        // Check audio probability
        if (Random.value > audioPlayProbability)
            return;

        // Check minimum time between events
        if (currentTime - lastAudioEventTime < minTimeBetweenEvents)
            return;

        // Check local simultaneous sound limit
        if (activeAudioInstances.Count >= maxSimultaneousLightningSounds)
            return;

        // Check distance to player
        if (playerTransform != null)
        {
            float distanceToPlayer = Vector3.Distance(position, playerTransform.position);
            if (distanceToPlayer > maxAudioDistance)
                return;
        }

        // Create traveling audio instance for this particle
        CreateTravelingAudioInstance(particleId, position);

        // Update timing
        lastAudioEventTime = currentTime;
        recentAudioTimes.Enqueue(currentTime);
    }

    private void PlayLightningViolinNote(Vector3 position)
    {
        // This method is now handled by CreateTravelingAudioInstance
        // Keeping for backward compatibility but not used
    }

    private void CreateTravelingAudioInstance(uint particleId, Vector3 position)
    {
        // Check if we already have an audio instance for this particle
        if (activeAudioInstances.ContainsKey(particleId))
            return;

        // Create FMOD event instance for traveling sound
        var eventInstance = RuntimeManager.CreateInstance(violinLightningEvent);
        if (!eventInstance.isValid())
            return;

        // Set initial position and start the sound
        eventInstance.set3DAttributes(RuntimeUtils.To3DAttributes(position));
        eventInstance.start();

        // Track this audio instance
        var audioInstance = new LightningAudioInstance(eventInstance, particleId);
        activeAudioInstances[particleId] = audioInstance;
    }

    private void UpdateTravelingAudioPositions()
    {
        if (activeAudioInstances.Count == 0)
            return;

        // Get current particles
        int particleCount = lightningParticleSystem.GetParticles(particlesArray);
        var currentParticlePositions = new Dictionary<uint, Vector3>();

        // Build lookup of current particle positions
        for (int i = 0; i < particleCount; i++)
        {
            uint particleId = particlesArray[i].randomSeed;
            currentParticlePositions[particleId] = particlesArray[i].position;
        }

        // Update audio positions and clean up dead particles
        var instancesToRemove = new List<uint>();
        foreach (var kvp in activeAudioInstances)
        {
            uint particleId = kvp.Key;
            var audioInstance = kvp.Value;

            // Check if particle still exists
            if (currentParticlePositions.TryGetValue(particleId, out Vector3 newPosition))
            {
                // Update audio position to follow the particle
                audioInstance.UpdatePosition(newPosition);
            }
            else
            {
                // Particle no longer exists, stop the audio and mark for removal
                audioInstance.Stop();
                instancesToRemove.Add(particleId);
            }
        }

        // Remove dead audio instances
        foreach (uint particleId in instancesToRemove)
        {
            activeAudioInstances.Remove(particleId);
        }
    }

    private void CleanupProcessedParticles(int currentParticleCount)
    {
        // If we have too many processed particles, clean up periodically
        if (processedParticles.Count > currentParticleCount * 2)
        {
            // Get current particle IDs
            var currentParticleIds = new HashSet<uint>();
            for (int i = 0; i < currentParticleCount; i++)
            {
                currentParticleIds.Add(particlesArray[i].randomSeed);
            }

            // Remove processed particles that no longer exist
            processedParticles.IntersectWith(currentParticleIds);
        }
    }

    #if UNITY_EDITOR
    private void OnDrawGizmosSelected()
    {
        // Draw audio range visualization in editor
        if (playerTransform != null)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(playerTransform.position, maxAudioDistance);
        }

        // Draw lightning particle positions if available
        if (lightningParticleSystem != null && Application.isPlaying)
        {
            int particleCount = lightningParticleSystem.GetParticles(particlesArray);
            Gizmos.color = Color.cyan;
            for (int i = 0; i < particleCount; i++)
            {
                Gizmos.DrawWireSphere(particlesArray[i].position, 0.5f);
            }
        }
    }
    #endif

    private void OnDisable()
    {
        // Clean up when disabled
        processedParticles.Clear();
        recentAudioTimes.Clear();

        // Stop and release all active audio instances
        foreach (var audioInstance in activeAudioInstances.Values)
        {
            audioInstance.Stop();
        }
        activeAudioInstances.Clear();
    }
}
