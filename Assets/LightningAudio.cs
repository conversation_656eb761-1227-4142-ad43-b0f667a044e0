using UnityEngine;
using FMODUnity;
using System.Collections.Generic;

/// <summary>
/// Lightning audio system that plays unique violin notes for each lightning bolt particle.
/// Integrates with the existing AudioManager LOD system for performance optimization.
/// </summary>
[RequireComponent(typeof(ParticleSystem))]
public class LightningAudio : MonoBehaviour
{
    [Header("FMOD Audio Settings")]
    [Tooltip("FMOD event for violin lightning notes")]
    [SerializeField] private EventReference violinLightningEvent;

    [<PERSON><PERSON>("Audio Behavior")]
    [Tooltip("Minimum time between lightning audio events (prevents audio spam)")]
    [SerializeField] private float minTimeBetweenEvents = 0.05f;

    [Tooltip("Maximum number of simultaneous lightning sounds (local throttling)")]
    [SerializeField] private int maxSimultaneousLightningSounds = 50;

    [Tooltip("Probability that a lightning bolt will play audio (0-1)")]
    [Range(0f, 1f)]
    [SerializeField] private float audioPlayProbability = 0.8f;

    [Head<PERSON>("Violin Note Variation")]
    [Tooltip("Minimum pitch multiplier for violin notes")]
    [SerializeField] private float minPitch = 0.5f;

    [Tooltip("Maximum pitch multiplier for violin notes")]
    [SerializeField] private float maxPitch = 2.0f;

    [Tooltip("Use discrete musical notes instead of random pitch")]
    [SerializeField] private bool useMusicalNotes = true;

    [Header("Performance Settings")]
    [Tooltip("How often to check for new particles (seconds)")]
    [SerializeField] private float particleCheckInterval = 0.02f;

    [Tooltip("Maximum distance from player to play lightning audio")]
    [SerializeField] private float maxAudioDistance = 100f;

    // Musical note pitch multipliers (C major scale)
    private readonly float[] musicalNotes = { 1.0f, 1.125f, 1.25f, 1.33f, 1.5f, 1.67f, 1.875f, 2.0f };

    // Component references
    private ParticleSystem lightningParticleSystem;
    private Transform playerTransform;

    // Particle tracking
    private ParticleSystem.Particle[] particlesArray;
    private HashSet<uint> processedParticles = new HashSet<uint>();
    private Queue<float> recentAudioTimes = new Queue<float>();

    // Timing
    private float lastParticleCheckTime;
    private float lastAudioEventTime;
    private int currentSimultaneousSounds;

    void Start()
    {
        InitializeComponents();
        InitializeParticleTracking();
    }

    void Update()
    {
        UpdateAudioThrottling();
        CheckForNewLightningBolts();
    }

    private void InitializeComponents()
    {
        lightningParticleSystem = GetComponent<ParticleSystem>();
        if (lightningParticleSystem == null)
        {
            Debug.LogError($"LightningAudio: No ParticleSystem found on {gameObject.name}!");
            enabled = false;
            return;
        }

        // Get player reference from GameManager
        if (GameManager.Instance != null && GameManager.Instance.player != null)
        {
            playerTransform = GameManager.Instance.player.transform;
        }
        else if (Camera.main != null)
        {
            playerTransform = Camera.main.transform;
        }
        else
        {
            Debug.LogWarning("LightningAudio: No player or camera found for distance calculations!");
        }

        if (violinLightningEvent.IsNull)
        {
            Debug.LogWarning($"LightningAudio: No violin lightning event assigned on {gameObject.name}!");
        }
    }

    private void InitializeParticleTracking()
    {
        // Initialize particle array with max particles capacity
        int maxParticles = lightningParticleSystem.main.maxParticles;
        particlesArray = new ParticleSystem.Particle[maxParticles];

        Debug.Log($"LightningAudio: Initialized for up to {maxParticles} lightning particles on {gameObject.name}");
    }

    private void UpdateAudioThrottling()
    {
        float currentTime = Time.time;

        // Clean up old audio event times (assume 1 second duration for lightning sounds)
        while (recentAudioTimes.Count > 0 && currentTime - recentAudioTimes.Peek() > 1.0f)
        {
            recentAudioTimes.Dequeue();
        }

        // Update current simultaneous sounds count
        currentSimultaneousSounds = recentAudioTimes.Count;
    }

    private void CheckForNewLightningBolts()
    {
        float currentTime = Time.time;

        // Throttle particle checking for performance
        if (currentTime - lastParticleCheckTime < particleCheckInterval)
            return;

        lastParticleCheckTime = currentTime;

        // Skip if no FMOD event assigned
        if (violinLightningEvent.IsNull)
            return;

        // Get current particles
        int particleCount = lightningParticleSystem.GetParticles(particlesArray);
        if (particleCount == 0)
            return;

        // Check for new particles and play audio
        for (int i = 0; i < particleCount; i++)
        {
            ref ParticleSystem.Particle particle = ref particlesArray[i];
            uint particleId = particle.randomSeed;

            // Skip if we've already processed this particle
            if (processedParticles.Contains(particleId))
                continue;

            // Mark particle as processed
            processedParticles.Add(particleId);

            // Try to play audio for this new lightning bolt
            TryPlayLightningAudio(particle.position, currentTime);
        }

        // Clean up processed particles that no longer exist
        CleanupProcessedParticles(particleCount);
    }

    private void TryPlayLightningAudio(Vector3 position, float currentTime)
    {
        // Check audio probability
        if (Random.value > audioPlayProbability)
            return;

        // Check minimum time between events
        if (currentTime - lastAudioEventTime < minTimeBetweenEvents)
            return;

        // Check local simultaneous sound limit
        if (currentSimultaneousSounds >= maxSimultaneousLightningSounds)
            return;

        // Check distance to player
        if (playerTransform != null)
        {
            float distanceToPlayer = Vector3.Distance(position, playerTransform.position);
            if (distanceToPlayer > maxAudioDistance)
                return;
        }

        // Play the lightning violin note
        PlayLightningViolinNote(position);

        // Update timing
        lastAudioEventTime = currentTime;
        recentAudioTimes.Enqueue(currentTime);
    }

    private void PlayLightningViolinNote(Vector3 position)
    {
        // Use AudioManager's PlayOneShot for integration with existing LOD system
        // This respects the global audio limits and distance-based optimization
        AudioManager.PlayOneShot(violinLightningEvent, position);

        // Note: If you want to add pitch variation, you would need to use PlayEventInstance
        // and set parameters before starting. For now, we rely on FMOD event randomization.
    }

    private void CleanupProcessedParticles(int currentParticleCount)
    {
        // If we have too many processed particles, clean up periodically
        if (processedParticles.Count > currentParticleCount * 2)
        {
            // Get current particle IDs
            var currentParticleIds = new HashSet<uint>();
            for (int i = 0; i < currentParticleCount; i++)
            {
                currentParticleIds.Add(particlesArray[i].randomSeed);
            }

            // Remove processed particles that no longer exist
            processedParticles.IntersectWith(currentParticleIds);
        }
    }

    #if UNITY_EDITOR
    private void OnDrawGizmosSelected()
    {
        // Draw audio range visualization in editor
        if (playerTransform != null)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(playerTransform.position, maxAudioDistance);
        }

        // Draw lightning particle positions if available
        if (lightningParticleSystem != null && Application.isPlaying)
        {
            int particleCount = lightningParticleSystem.GetParticles(particlesArray);
            Gizmos.color = Color.cyan;
            for (int i = 0; i < particleCount; i++)
            {
                Gizmos.DrawWireSphere(particlesArray[i].position, 0.5f);
            }
        }
    }
    #endif

    private void OnDisable()
    {
        // Clean up when disabled
        processedParticles.Clear();
        recentAudioTimes.Clear();
        currentSimultaneousSounds = 0;
    }
}
