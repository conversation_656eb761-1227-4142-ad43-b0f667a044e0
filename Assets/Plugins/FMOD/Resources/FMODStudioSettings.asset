%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8358495765050620160
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 9587379c972e4a54da1949613f11e1c6, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: default
  parentIdentifier: 
  active: 1
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 9264
      HasValue: 1
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 44100
      HasValue: 1
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 1024
      HasValue: 1
    RealChannelCount:
      Value: 256
      HasValue: 1
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 1
    StaticPlugins:
      Value: []
      HasValue: 1
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: 
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers:
  - b7716510a1f36934c87976f3a81dbf3d
  - 46fbfdf3fc43db0458918377fd40293e
  - c88d16e5272a4e241b0ef0ac2e53b73d
  - 0f8eb3f400726694eb47beb1a9f94ce8
  - 52eb9df5db46521439908db3a29a1bbb
  - de700ef3f37a49b58a57ae3addf01ad9
  - 2fea114e74ecf3c4f920e1d5cc1c4c40
  - e7a046c753c3c3d4aacc91f6597f310d
  - fd7c55dab0fce234b8c25f6ffca523c1
  - 2c5177b11d81d824dbb064f9ac8527da
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &-5806299306725553217
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 73549a74f689f0849a8271d9e908c514, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: b7716510a1f36934c87976f3a81dbf3d
  parentIdentifier: default
  active: 0
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 0
      HasValue: 0
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 0
      HasValue: 0
    RealChannelCount:
      Value: 0
      HasValue: 0
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 0
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: 
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &-458677101035787001
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: b87314b32fbe18943af496e4b47136c6, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: 46fbfdf3fc43db0458918377fd40293e
  parentIdentifier: default
  active: 0
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 0
      HasValue: 0
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 0
      HasValue: 0
    RealChannelCount:
      Value: 0
      HasValue: 0
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 0
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: 
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: eef8d824ea7b63742966aaa0e94ac383, type: 3}
  m_Name: FMODStudioSettings
  m_EditorClassIdentifier: 
  switchSettingsMigration: 1
  HasSourceProject: 1
  HasPlatforms: 1
  sourceProjectPath: ../../Documents/Fmod Projects/Soul/Soul2/Soul2.fspro
  sourceBankPath: ../../Documents/Fmod Projects/Soul/Soul2/Build
  sourceBankPathUnformatted: 
  BankRefreshCooldown: 1
  ShowBankRefreshWindow: 0
  AutomaticEventLoading: 1
  BankLoadType: 0
  AutomaticSampleLoading: 0
  EncryptionKey: 
  ImportType: 0
  TargetAssetPath: FMODBanks
  TargetBankFolder: 
  EventLinkage: 0
  LoggingLevel: 2
  SpeakerModeSettings: []
  SampleRateSettings: []
  LiveUpdateSettings: []
  OverlaySettings: []
  BankDirectorySettings: []
  VirtualChannelSettings: []
  RealChannelSettings: []
  Plugins: []
  MasterBanks:
  - MainBank
  Banks:
  - Clouds
  - Ocean
  - SnakeRiver
  - SoulCreature1
  - SoulCreature2
  - SoulCreatureGiant
  BanksToLoad: []
  LiveUpdatePort: 9264
  EnableMemoryTracking: 1
  AndroidUseOBB: 0
  AndroidPatchBuild: 0
  MeterChannelOrdering: 0
  StopEventsOutsideMaxDistance: 0
  BoltUnitOptionsBuildPending: 0
  EnableErrorCallback: 0
  SharedLibraryUpdateStage: 0
  SharedLibraryTimeSinceStart: 0
  CurrentVersion: 131846
  HideSetupWizard: 1
  LastEventReferenceScanVersion: 131623
  Platforms:
  - {fileID: -8358495765050620160}
  - {fileID: -5806299306725553217}
  - {fileID: -458677101035787001}
  - {fileID: 414272481810028494}
  - {fileID: 473236500597395511}
  - {fileID: 1440861712410404526}
  - {fileID: 5825449032712442706}
  - {fileID: 6816506753297711358}
  - {fileID: 6969352325402509593}
  - {fileID: 7075436975440853166}
  - {fileID: 7286835829551511024}
  - {fileID: 8207828572624250892}
  MigratedPlatforms: 0500000000000000060000000a0000000800000009000000120000000c000000150000000b0000000200000001000000
--- !u!114 &414272481810028494
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 3abeb1429547a134480f4f2f1efc7e21, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: c88d16e5272a4e241b0ef0ac2e53b73d
  parentIdentifier: default
  active: 0
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 0
      HasValue: 0
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 0
      HasValue: 0
    RealChannelCount:
      Value: 0
      HasValue: 0
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 0
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: 
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &473236500597395511
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: bc1f51bc35d549941904cf062bae93a3, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: 0f8eb3f400726694eb47beb1a9f94ce8
  parentIdentifier: default
  active: 0
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 0
      HasValue: 0
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 0
      HasValue: 0
    RealChannelCount:
      Value: 0
      HasValue: 0
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 0
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: 
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &1440861712410404526
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d92d0baf34dae0e4ea032a42bd6107c0, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: 52eb9df5db46521439908db3a29a1bbb
  parentIdentifier: default
  active: 1
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 0
      HasValue: 0
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 1024
      HasValue: 1
    RealChannelCount:
      Value: 256
      HasValue: 1
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 1
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: AUTODETECT
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &5825449032712442706
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 8f591a193113347409efedcc618e83d6, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: de700ef3f37a49b58a57ae3addf01ad9
  parentIdentifier: default
  active: 0
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 0
      HasValue: 0
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 0
      HasValue: 0
    RealChannelCount:
      Value: 0
      HasValue: 0
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 0
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: 
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &6816506753297711358
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 97ba6cc2660c0ca498540d254701057a, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: 2fea114e74ecf3c4f920e1d5cc1c4c40
  parentIdentifier: default
  active: 0
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 0
      HasValue: 0
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 0
      HasValue: 0
    RealChannelCount:
      Value: 0
      HasValue: 0
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 0
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: 
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &6969352325402509593
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 17eea195bdfbf014e91ba7620ee491f8, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: e7a046c753c3c3d4aacc91f6597f310d
  parentIdentifier: default
  active: 0
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 0
      HasValue: 0
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 0
      HasValue: 0
    RealChannelCount:
      Value: 0
      HasValue: 0
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 0
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: 
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &7075436975440853166
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 9660e62d6232af242877f0cc2b90c63d, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: 2c5177b11d81d824dbb064f9ac8527da
  parentIdentifier: default
  active: 1
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 0
      HasValue: 0
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 1024
      HasValue: 1
    RealChannelCount:
      Value: 256
      HasValue: 1
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 0
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: AUTODETECT
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 1
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &7286835829551511024
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 335f0a4b26fb46942858ea029e030d2a, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: playInEditor
  parentIdentifier: 
  active: 1
  Properties:
    LiveUpdate:
      Value: 1
      HasValue: 1
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 1
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 44100
      HasValue: 1
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 1024
      HasValue: 1
    RealChannelCount:
      Value: 256
      HasValue: 1
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 0
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: 
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
--- !u!114 &8207828572624250892
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 93a382382f106584e8f8f62412fee177, type: 3}
  m_Name: FMODStudioSettingsPlatform
  m_EditorClassIdentifier: 
  identifier: fd7c55dab0fce234b8c25f6ffca523c1
  parentIdentifier: default
  active: 0
  Properties:
    LiveUpdate:
      Value: 0
      HasValue: 0
    LiveUpdatePort:
      Value: 0
      HasValue: 0
    Overlay:
      Value: 0
      HasValue: 0
    OverlayPosition:
      Value: 0
      HasValue: 0
    OverlayFontSize:
      Value: 0
      HasValue: 0
    Logging:
      Value: 0
      HasValue: 0
    SampleRate:
      Value: 0
      HasValue: 0
    BuildDirectory:
      Value: 
      HasValue: 0
    SpeakerMode:
      Value: 0
      HasValue: 0
    VirtualChannelCount:
      Value: 0
      HasValue: 0
    RealChannelCount:
      Value: 0
      HasValue: 0
    DSPBufferLength:
      Value: 0
      HasValue: 0
    DSPBufferCount:
      Value: 0
      HasValue: 0
    Plugins:
      Value: []
      HasValue: 0
    StaticPlugins:
      Value: []
      HasValue: 0
    CallbackHandler:
      Value: {fileID: 0}
      HasValue: 0
  OutputTypeName: 
  threadAffinities:
    Value: []
    HasValue: 0
  displaySortOrder: 0
  childIdentifiers: []
  codecChannels:
    Value: []
    HasValue: 0
