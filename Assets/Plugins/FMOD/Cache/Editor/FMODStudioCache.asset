%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9182123178018528059
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreatureGiant_sound2
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreatureGiant_sound2
  Guid:
    Data1: -*********
    Data2: **********
    Data3: -*********
    Data4: -*********
  Banks:
  - {fileID: -5819748518531221150}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 10
  Length: 3500
--- !u!114 &-9033867481578922140
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_va_sound2_G2
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_va_sound2_G2
  Guid:
    Data1: -**********
    Data2: **********
    Data3: -*********
    Data4: -**********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 10
  Length: 1500
--- !u!114 &-8847983466688067288
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_va_sound1_G5
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_va_sound1_G5
  Guid:
    Data1: **********
    Data2: **********
    Data3: -**********
    Data4: -**********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3500
--- !u!114 &-8836439001287940403
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_va_sound2_G5
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_va_sound2_G5
  Guid:
    Data1: *********
    Data2: **********
    Data3: **********
    Data4: **********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 10
  Length: 1500
--- !u!114 &-8677929342386169699
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: c18180ecb35941f4682ae60107b85b7c, type: 3}
  m_Name: bank:/SnakeRiver.bank
  m_EditorClassIdentifier: 
  Path: ../../Documents/Fmod Projects/Soul/Soul2/Build/Desktop/SnakeRiver.bank
  Name: SnakeRiver
  StudioPath: bank:/SnakeRiver
  lastModified: 638843328945296572
  FileSizes:
  - Name: Desktop
    Value: 1513984
  Exists: 1
--- !u!114 &-8639699597614743801
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_sound2_G3
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_sound2_G3
  Guid:
    Data1: -**********
    Data2: **********
    Data3: -*********
    Data4: -**********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 600
--- !u!114 &-7012682398692464038
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_va_sound2_G4
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_va_sound2_G4
  Guid:
    Data1: -**********
    Data2: **********
    Data3: *********
    Data4: *********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 10
  Length: 1500
--- !u!114 &-5819748518531221150
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: c18180ecb35941f4682ae60107b85b7c, type: 3}
  m_Name: bank:/SoulCreatureGiant.bank
  m_EditorClassIdentifier: 
  Path: ../../Documents/Fmod Projects/Soul/Soul2/Build/Desktop/SoulCreatureGiant.bank
  Name: SoulCreatureGiant
  StudioPath: bank:/SoulCreatureGiant
  lastModified: 638839596645754581
  FileSizes:
  - Name: Desktop
    Value: 1326912
  Exists: 1
--- !u!114 &-5276725847726935895
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_sound2_G1
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_sound2_G1
  Guid:
    Data1: *********
    Data2: **********
    Data3: *********
    Data4: **********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 600
--- !u!114 &-5064778509113085546
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SnakeRiver/SnakeRiver
  m_EditorClassIdentifier: 
  Path: event:/SnakeRiver/SnakeRiver
  Guid:
    Data1: -**********
    Data2: **********
    Data3: *********
    Data4: -********
  Banks:
  - {fileID: -8677929342386169699}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 10
  Length: 3000
--- !u!114 &-4640131847137762822
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_sound2_G4
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_sound2_G4
  Guid:
    Data1: -**********
    Data2: **********
    Data3: -**********
    Data4: -**********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 600
--- !u!114 &-4440881607999416032
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: c18180ecb35941f4682ae60107b85b7c, type: 3}
  m_Name: bank:/MainBank.strings.bank
  m_EditorClassIdentifier: 
  Path: ../../Documents/Fmod Projects/Soul/Soul2/Build/Desktop/MainBank.strings.bank
  Name: MainBank.strings
  StudioPath: bank:/MainBank.strings
  lastModified: 638843328945068099
  FileSizes:
  - Name: Desktop
    Value: 2278
  Exists: 1
--- !u!114 &-4158106527342660865
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: c18180ecb35941f4682ae60107b85b7c, type: 3}
  m_Name: bank:/MainBank.bank
  m_EditorClassIdentifier: 
  Path: ../../Documents/Fmod Projects/Soul/Soul2/Build/Desktop/MainBank.bank
  Name: MainBank
  StudioPath: bank:/MainBank
  lastModified: 638843328945047353
  FileSizes:
  - Name: Desktop
    Value: 1019776
  Exists: 1
--- !u!114 &-3474156716632869387
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/OceanBottom/OceanBottom
  m_EditorClassIdentifier: 
  Path: event:/OceanBottom/OceanBottom
  Guid:
    Data1: **********
    Data2: **********
    Data3: *********
    Data4: -*********
  Banks:
  - {fileID: -3420931811683264372}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters:
  - {fileID: 9048064249566682611}
  MinDistance: 1
  MaxDistance: 20
  Length: 6000
--- !u!114 &-3420931811683264372
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: c18180ecb35941f4682ae60107b85b7c, type: 3}
  m_Name: bank:/Ocean.bank
  m_EditorClassIdentifier: 
  Path: ../../Documents/Fmod Projects/Soul/Soul2/Build/Desktop/Ocean.bank
  Name: Ocean
  StudioPath: bank:/Ocean
  lastModified: 638840713767621612
  FileSizes:
  - Name: Desktop
    Value: 1249888
  Exists: 1
--- !u!114 &-1935692860686213712
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_sound1_G3
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_sound1_G3
  Guid:
    Data1: **********
    Data2: **********
    Data3: **********
    Data4: -*********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3500
--- !u!114 &-1478972734722565984
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_va_sound2_G1
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_va_sound2_G1
  Guid:
    Data1: *********
    Data2: **********
    Data3: ********
    Data4: -**********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 10
  Length: 1500
--- !u!114 &-570087085890122733
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature1_sound2
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature1_sound2
  Guid:
    Data1: -*********
    Data2: **********
    Data3: -*********
    Data4: **********
  Banks:
  - {fileID: 5818919982784982406}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters:
  - {fileID: 983623372359446116}
  MinDistance: 0
  MaxDistance: 20
  Length: 996
--- !u!114 &-185942717629678619
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: c18180ecb35941f4682ae60107b85b7c, type: 3}
  m_Name: bank:/Clouds.bank
  m_EditorClassIdentifier: 
  Path: ../../Documents/Fmod Projects/Soul/Soul2/Build/Desktop/Clouds.bank
  Name: Clouds
  StudioPath: bank:/Clouds
  lastModified: 638843328948262749
  FileSizes:
  - Name: Desktop
    Value: 2302688
  Exists: 1
--- !u!114 &********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d32cf7c32a3ed8347bac48ef5ed56d82, type: 3}
  m_Name: FMODStudioCache
  m_EditorClassIdentifier: 
  EditorBanks:
  - {fileID: -4440881607999416032}
  - {fileID: -4158106527342660865}
  - {fileID: 5818919982784982406}
  - {fileID: -3420931811683264372}
  - {fileID: 832708963840528506}
  - {fileID: -5819748518531221150}
  - {fileID: -8677929342386169699}
  - {fileID: -185942717629678619}
  EditorEvents:
  - {fileID: 6997840255720261216}
  - {fileID: -3474156716632869387}
  - {fileID: 3970207212587290534}
  - {fileID: -5276725847726935895}
  - {fileID: 7417699764179604594}
  - {fileID: -4640131847137762822}
  - {fileID: 7090674436081419379}
  - {fileID: -1935692860686213712}
  - {fileID: 2304476727628519232}
  - {fileID: 3139689562284227304}
  - {fileID: -8639699597614743801}
  - {fileID: 504313278254369233}
  - {fileID: 2074206541594473639}
  - {fileID: -8847983466688067288}
  - {fileID: -9033867481578922140}
  - {fileID: -1478972734722565984}
  - {fileID: 2703160155505675080}
  - {fileID: -7012682398692464038}
  - {fileID: 4382918534352867356}
  - {fileID: 991290352037344046}
  - {fileID: -8836439001287940403}
  - {fileID: 2074155209534075488}
  - {fileID: 426219455323853347}
  - {fileID: -5064778509113085546}
  - {fileID: 8511142211725092447}
  - {fileID: -570087085890122733}
  - {fileID: 6442287653798003325}
  - {fileID: -9182123178018528059}
  - {fileID: 1132103918682385129}
  EditorParameters:
  - {fileID: 6040128532785232615}
  MasterBanks:
  - {fileID: -4158106527342660865}
  StringsBanks:
  - {fileID: -4440881607999416032}
  cacheTime: 638843328948262749
  cacheVersion: 131846
--- !u!114 &426219455323853347
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreatureGiant_sound1
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreatureGiant_sound1
  Guid:
    Data1: -**********
    Data2: **********
    Data3: -*********
    Data4: **********
  Banks:
  - {fileID: -5819748518531221150}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3000
--- !u!114 &504313278254369233
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_sound2_G2
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_sound2_G2
  Guid:
    Data1: **********
    Data2: **********
    Data3: -**********
    Data4: -*********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 600
--- !u!114 &832708963840528506
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: c18180ecb35941f4682ae60107b85b7c, type: 3}
  m_Name: bank:/SoulCreature2.bank
  m_EditorClassIdentifier: 
  Path: ../../Documents/Fmod Projects/Soul/Soul2/Build/Desktop/SoulCreature2.bank
  Name: SoulCreature2
  StudioPath: bank:/SoulCreature2
  lastModified: 638843328946606737
  FileSizes:
  - Name: Desktop
    Value: 6265472
  Exists: 1
--- !u!114 &983623372359446116
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fecb8ef7f94ca804a8ab72049b86782e, type: 3}
  m_Name: parameter:/SoulCreature1_sound2/OceanLowpass
  m_EditorClassIdentifier: 
  Name: OceanLowpass
  StudioPath: 
  Min: 0
  Max: 1
  Default: 0
  ID:
    data1: **********
    data2: ********
  Type: 1
  IsGlobal: 1
  Labels: []
  Exists: 1
--- !u!114 &991290352037344046
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_va_sound1_G1
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_va_sound1_G1
  Guid:
    Data1: *********
    Data2: **********
    Data3: **********
    Data4: -**********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3500
--- !u!114 &1132103918682385129
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/Clouds/Lightning
  m_EditorClassIdentifier: 
  Path: event:/Clouds/Lightning
  Guid:
    Data1: -**********
    Data2: **********
    Data3: -**********
    Data4: -**********
  Banks:
  - {fileID: -185942717629678619}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 10
  Length: 3000
--- !u!114 &2074155209534075488
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_va_sound1_G3
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_va_sound1_G3
  Guid:
    Data1: -*********
    Data2: **********
    Data3: -**********
    Data4: -*********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3500
--- !u!114 &2074206541594473639
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_va_sound2_G3
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_va_sound2_G3
  Guid:
    Data1: *********
    Data2: **********
    Data3: -*********
    Data4: *********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 10
  Length: 1500
--- !u!114 &2304476727628519232
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_sound1_G4
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_sound1_G4
  Guid:
    Data1: **********
    Data2: **********
    Data3: **********
    Data4: -**********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3500
--- !u!114 &2703160155505675080
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_va_sound1_G4
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_va_sound1_G4
  Guid:
    Data1: -*********
    Data2: **********
    Data3: ********
    Data4: *********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3500
--- !u!114 &3139689562284227304
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_sound2_G5
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_sound2_G5
  Guid:
    Data1: -**********
    Data2: **********
    Data3: -*********
    Data4: *********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 10
  Length: 600
--- !u!114 &3970207212587290534
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_sound1_G2
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_sound1_G2
  Guid:
    Data1: **********
    Data2: **********
    Data3: -**********
    Data4: *********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3500
--- !u!114 &4382918534352867356
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_va_sound1_G2
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_va_sound1_G2
  Guid:
    Data1: -**********
    Data2: **********
    Data3: *********
    Data4: **********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3500
--- !u!114 &5818919982784982406
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: c18180ecb35941f4682ae60107b85b7c, type: 3}
  m_Name: bank:/SoulCreature1.bank
  m_EditorClassIdentifier: 
  Path: ../../Documents/Fmod Projects/Soul/Soul2/Build/Desktop/SoulCreature1.bank
  Name: SoulCreature1
  StudioPath: bank:/SoulCreature1
  lastModified: 638839596644232696
  FileSizes:
  - Name: Desktop
    Value: 185472
  Exists: 1
--- !u!114 &6040128532785232615
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fecb8ef7f94ca804a8ab72049b86782e, type: 3}
  m_Name: parameter:/OceanLowpass
  m_EditorClassIdentifier: 
  Name: OceanLowpass
  StudioPath: parameter:/OceanLowpass
  Min: 0
  Max: 1
  Default: 0
  ID:
    data1: **********
    data2: ********
  Type: 1
  IsGlobal: 1
  Labels: []
  Exists: 1
--- !u!114 &6442287653798003325
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature1_sound1
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature1_sound1
  Guid:
    Data1: **********
    Data2: **********
    Data3: -********
    Data4: **********
  Banks:
  - {fileID: 5818919982784982406}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 1000
--- !u!114 &6997840255720261216
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/Piano/Piano1
  m_EditorClassIdentifier: 
  Path: event:/Piano/Piano1
  Guid:
    Data1: -*********
    Data2: **********
    Data3: -**********
    Data4: **********
  Banks:
  - {fileID: -4158106527342660865}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 15
  Length: 3500
--- !u!114 &7090674436081419379
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_sound1_G1
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_sound1_G1
  Guid:
    Data1: -********
    Data2: **********
    Data3: *********
    Data4: **********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3500
--- !u!114 &7417699764179604594
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SoulCreatures/SoulCreature2_sound1_G5
  m_EditorClassIdentifier: 
  Path: event:/SoulCreatures/SoulCreature2_sound1_G5
  Guid:
    Data1: -**********
    Data2: **********
    Data3: *********
    Data4: -*********
  Banks:
  - {fileID: 832708963840528506}
  IsStream: 0
  Is3D: 1
  IsOneShot: 1
  Parameters: []
  MinDistance: 0
  MaxDistance: 20
  Length: 3500
--- !u!114 &8511142211725092447
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: ceb653cd98e289a4e8697a1af55201f2, type: 3}
  m_Name: event:/SnakeRiver/Silence_1s
  m_EditorClassIdentifier: 
  Path: event:/SnakeRiver/Silence_1s
  Guid:
    Data1: -**********
    Data2: **********
    Data3: **********
    Data4: **********
  Banks:
  - {fileID: -8677929342386169699}
  IsStream: 0
  Is3D: 0
  IsOneShot: 1
  Parameters: []
  MinDistance: 1
  MaxDistance: 20
  Length: 1000
--- !u!114 &9048064249566682611
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fecb8ef7f94ca804a8ab72049b86782e, type: 3}
  m_Name: parameter:/OceanBottom/OceanLowpass
  m_EditorClassIdentifier: 
  Name: OceanLowpass
  StudioPath: 
  Min: 0
  Max: 1
  Default: 0
  ID:
    data1: **********
    data2: ********
  Type: 1
  IsGlobal: 1
  Labels: []
  Exists: 1
