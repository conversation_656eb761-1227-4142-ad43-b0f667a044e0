{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 74813, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 74813, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 74813, "tid": 1602569, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 74813, "tid": 1602569, "ts": 1748724917814165, "dur": 580, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 74813, "tid": 1602569, "ts": 1748724917824649, "dur": 447, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 74813, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 74813, "tid": 1, "ts": 1748724916849219, "dur": 13055, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 74813, "tid": 1, "ts": 1748724916862279, "dur": 69128, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 74813, "tid": 1, "ts": 1748724916931415, "dur": 37152, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 74813, "tid": 1602569, "ts": 1748724917825100, "dur": 15, "ph": "X", "name": "", "args": {}}, {"pid": 74813, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916846223, "dur": 19942, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916866168, "dur": 917243, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916867774, "dur": 5009, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916872797, "dur": 1049, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916873848, "dur": 11388, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916885325, "dur": 411, "ph": "X", "name": "ProcessMessages 3758", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916885739, "dur": 37, "ph": "X", "name": "ReadAsync 3758", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916885799, "dur": 6, "ph": "X", "name": "ProcessMessages 8146", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916885806, "dur": 69, "ph": "X", "name": "ReadAsync 8146", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916885885, "dur": 2, "ph": "X", "name": "ProcessMessages 2598", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916885888, "dur": 71, "ph": "X", "name": "ReadAsync 2598", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886026, "dur": 2, "ph": "X", "name": "ProcessMessages 2507", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886030, "dur": 27, "ph": "X", "name": "ReadAsync 2507", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886058, "dur": 2, "ph": "X", "name": "ProcessMessages 2989", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886061, "dur": 37, "ph": "X", "name": "ReadAsync 2989", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886110, "dur": 1, "ph": "X", "name": "ProcessMessages 1305", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886123, "dur": 508, "ph": "X", "name": "ReadAsync 1305", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886632, "dur": 2, "ph": "X", "name": "ProcessMessages 4666", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886635, "dur": 29, "ph": "X", "name": "ReadAsync 4666", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886673, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886677, "dur": 45, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886723, "dur": 1, "ph": "X", "name": "ProcessMessages 1739", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886725, "dur": 63, "ph": "X", "name": "ReadAsync 1739", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886789, "dur": 1, "ph": "X", "name": "ProcessMessages 1244", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886790, "dur": 60, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886852, "dur": 1, "ph": "X", "name": "ProcessMessages 2219", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886853, "dur": 63, "ph": "X", "name": "ReadAsync 2219", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886918, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916886920, "dur": 187, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887108, "dur": 6, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887115, "dur": 346, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887463, "dur": 23, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887489, "dur": 58, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887549, "dur": 43, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887594, "dur": 43, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887640, "dur": 58, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887699, "dur": 1, "ph": "X", "name": "ProcessMessages 1498", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887701, "dur": 36, "ph": "X", "name": "ReadAsync 1498", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887739, "dur": 177, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887918, "dur": 55, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916887976, "dur": 32, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888011, "dur": 249, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888279, "dur": 1, "ph": "X", "name": "ProcessMessages 2194", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888291, "dur": 51, "ph": "X", "name": "ReadAsync 2194", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888343, "dur": 1, "ph": "X", "name": "ProcessMessages 2063", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888345, "dur": 30, "ph": "X", "name": "ReadAsync 2063", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888377, "dur": 40, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888420, "dur": 34, "ph": "X", "name": "ReadAsync 1116", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888456, "dur": 214, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888673, "dur": 40, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888714, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888716, "dur": 47, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888766, "dur": 49, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888817, "dur": 53, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888873, "dur": 121, "ph": "X", "name": "ReadAsync 1703", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916888998, "dur": 1, "ph": "X", "name": "ProcessMessages 1931", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889000, "dur": 33, "ph": "X", "name": "ReadAsync 1931", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889034, "dur": 1, "ph": "X", "name": "ProcessMessages 1656", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889036, "dur": 105, "ph": "X", "name": "ReadAsync 1656", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889142, "dur": 1, "ph": "X", "name": "ProcessMessages 1346", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889155, "dur": 34, "ph": "X", "name": "ReadAsync 1346", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889191, "dur": 1, "ph": "X", "name": "ProcessMessages 1483", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889192, "dur": 86, "ph": "X", "name": "ReadAsync 1483", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889284, "dur": 5, "ph": "X", "name": "ProcessMessages 1324", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889291, "dur": 98, "ph": "X", "name": "ReadAsync 1324", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889402, "dur": 2, "ph": "X", "name": "ProcessMessages 2101", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889405, "dur": 55, "ph": "X", "name": "ReadAsync 2101", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889463, "dur": 1, "ph": "X", "name": "ProcessMessages 2322", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889483, "dur": 30, "ph": "X", "name": "ReadAsync 2322", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889524, "dur": 285, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889810, "dur": 1, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889812, "dur": 30, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889843, "dur": 1, "ph": "X", "name": "ProcessMessages 1741", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889844, "dur": 24, "ph": "X", "name": "ReadAsync 1741", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889870, "dur": 57, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889930, "dur": 1, "ph": "X", "name": "ProcessMessages 1384", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889931, "dur": 24, "ph": "X", "name": "ReadAsync 1384", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889958, "dur": 28, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916889996, "dur": 33, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890031, "dur": 26, "ph": "X", "name": "ReadAsync 1415", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890061, "dur": 24, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890086, "dur": 28, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890116, "dur": 37, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890155, "dur": 29, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890186, "dur": 39, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890227, "dur": 22, "ph": "X", "name": "ReadAsync 1238", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890252, "dur": 64, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890317, "dur": 1, "ph": "X", "name": "ProcessMessages 1661", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890318, "dur": 34, "ph": "X", "name": "ReadAsync 1661", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890354, "dur": 1, "ph": "X", "name": "ProcessMessages 1534", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890356, "dur": 54, "ph": "X", "name": "ReadAsync 1534", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890411, "dur": 1, "ph": "X", "name": "ProcessMessages 1649", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890413, "dur": 92, "ph": "X", "name": "ReadAsync 1649", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890506, "dur": 1, "ph": "X", "name": "ProcessMessages 2413", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890508, "dur": 38, "ph": "X", "name": "ReadAsync 2413", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890548, "dur": 1, "ph": "X", "name": "ProcessMessages 1409", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890549, "dur": 51, "ph": "X", "name": "ReadAsync 1409", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890613, "dur": 98, "ph": "X", "name": "ReadAsync 1266", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890712, "dur": 1, "ph": "X", "name": "ProcessMessages 2699", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890713, "dur": 26, "ph": "X", "name": "ReadAsync 2699", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890742, "dur": 136, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890880, "dur": 1, "ph": "X", "name": "ProcessMessages 3206", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890882, "dur": 49, "ph": "X", "name": "ReadAsync 3206", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890932, "dur": 1, "ph": "X", "name": "ProcessMessages 1310", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890934, "dur": 57, "ph": "X", "name": "ReadAsync 1310", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890995, "dur": 1, "ph": "X", "name": "ProcessMessages 1784", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916890996, "dur": 55, "ph": "X", "name": "ReadAsync 1784", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916891095, "dur": 111, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916891221, "dur": 2, "ph": "X", "name": "ProcessMessages 2276", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916891224, "dur": 32, "ph": "X", "name": "ReadAsync 2276", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916891260, "dur": 1, "ph": "X", "name": "ProcessMessages 1384", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916891262, "dur": 42, "ph": "X", "name": "ReadAsync 1384", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916891306, "dur": 53, "ph": "X", "name": "ReadAsync 1323", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916891457, "dur": 31, "ph": "X", "name": "ReadAsync 1424", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916891490, "dur": 2, "ph": "X", "name": "ProcessMessages 3721", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916891493, "dur": 27, "ph": "X", "name": "ReadAsync 3721", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916891522, "dur": 457, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892031, "dur": 5, "ph": "X", "name": "ProcessMessages 8172", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892044, "dur": 74, "ph": "X", "name": "ReadAsync 8172", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892119, "dur": 1, "ph": "X", "name": "ProcessMessages 1928", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892121, "dur": 73, "ph": "X", "name": "ReadAsync 1928", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892196, "dur": 1, "ph": "X", "name": "ProcessMessages 1713", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892198, "dur": 45, "ph": "X", "name": "ReadAsync 1713", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892350, "dur": 61, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892429, "dur": 2, "ph": "X", "name": "ProcessMessages 3803", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892432, "dur": 57, "ph": "X", "name": "ReadAsync 3803", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892529, "dur": 1, "ph": "X", "name": "ProcessMessages 2314", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892531, "dur": 50, "ph": "X", "name": "ReadAsync 2314", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892611, "dur": 1, "ph": "X", "name": "ProcessMessages 2941", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892613, "dur": 51, "ph": "X", "name": "ReadAsync 2941", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892664, "dur": 1, "ph": "X", "name": "ProcessMessages 2159", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892666, "dur": 128, "ph": "X", "name": "ReadAsync 2159", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892796, "dur": 1, "ph": "X", "name": "ProcessMessages 2652", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916892798, "dur": 371, "ph": "X", "name": "ReadAsync 2652", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893172, "dur": 4, "ph": "X", "name": "ProcessMessages 6831", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893176, "dur": 126, "ph": "X", "name": "ReadAsync 6831", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893306, "dur": 113, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893421, "dur": 1, "ph": "X", "name": "ProcessMessages 1231", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893423, "dur": 46, "ph": "X", "name": "ReadAsync 1231", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893470, "dur": 1, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893474, "dur": 28, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893504, "dur": 39, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893550, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893551, "dur": 87, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893640, "dur": 1, "ph": "X", "name": "ProcessMessages 1582", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893647, "dur": 49, "ph": "X", "name": "ReadAsync 1582", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893697, "dur": 1, "ph": "X", "name": "ProcessMessages 1757", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893699, "dur": 20, "ph": "X", "name": "ReadAsync 1757", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916893721, "dur": 336, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916894059, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916894061, "dur": 262, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916894325, "dur": 10, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916894337, "dur": 27, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916894439, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916894441, "dur": 214, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916894657, "dur": 183, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916894843, "dur": 254, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916895100, "dur": 147, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916895250, "dur": 80, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916895331, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916895341, "dur": 176, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916895520, "dur": 227, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916895749, "dur": 365, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916896117, "dur": 364, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916896483, "dur": 151, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916896640, "dur": 258, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916896900, "dur": 189, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916897122, "dur": 34, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916897160, "dur": 231, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916897397, "dur": 138, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916897536, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916897537, "dur": 275, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916897822, "dur": 117, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916897940, "dur": 11, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916897952, "dur": 827, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916898807, "dur": 7, "ph": "X", "name": "ProcessMessages 2253", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916898817, "dur": 169, "ph": "X", "name": "ReadAsync 2253", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916898989, "dur": 3, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916898994, "dur": 259, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916899255, "dur": 2, "ph": "X", "name": "ProcessMessages 2517", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916899279, "dur": 401, "ph": "X", "name": "ReadAsync 2517", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916899683, "dur": 247, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916899937, "dur": 3, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916899942, "dur": 214, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916900158, "dur": 2, "ph": "X", "name": "ProcessMessages 2219", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916900161, "dur": 166, "ph": "X", "name": "ReadAsync 2219", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916900329, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916900331, "dur": 324, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916900708, "dur": 6, "ph": "X", "name": "ProcessMessages 2648", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916900719, "dur": 279, "ph": "X", "name": "ReadAsync 2648", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916901037, "dur": 4, "ph": "X", "name": "ProcessMessages 1176", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916901043, "dur": 246, "ph": "X", "name": "ReadAsync 1176", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916901292, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916901295, "dur": 186, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916901482, "dur": 1, "ph": "X", "name": "ProcessMessages 2724", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916901485, "dur": 282, "ph": "X", "name": "ReadAsync 2724", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916901771, "dur": 3, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916901787, "dur": 314, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916902128, "dur": 4, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916902135, "dur": 262, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916902399, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916902400, "dur": 314, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916902721, "dur": 2, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916902726, "dur": 2067, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916904796, "dur": 4, "ph": "X", "name": "ProcessMessages 5704", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916904802, "dur": 111, "ph": "X", "name": "ReadAsync 5704", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916904928, "dur": 211, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916905143, "dur": 213, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916905358, "dur": 297, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916905695, "dur": 151, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916905876, "dur": 211, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916906090, "dur": 183, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916906276, "dur": 165, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916906509, "dur": 12, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916906525, "dur": 29, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916906558, "dur": 186, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916906748, "dur": 210, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916906961, "dur": 195, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907159, "dur": 157, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907318, "dur": 97, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907418, "dur": 1, "ph": "X", "name": "ProcessMessages 1694", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907419, "dur": 54, "ph": "X", "name": "ReadAsync 1694", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907475, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907476, "dur": 177, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907655, "dur": 26, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907683, "dur": 248, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907933, "dur": 8, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907943, "dur": 24, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916907969, "dur": 175, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916908147, "dur": 20, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916908170, "dur": 147, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916908320, "dur": 361, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916908693, "dur": 2, "ph": "X", "name": "ProcessMessages 2352", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916908696, "dur": 49, "ph": "X", "name": "ReadAsync 2352", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916908794, "dur": 28, "ph": "X", "name": "ReadAsync 1256", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916908824, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916908827, "dur": 207, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916909036, "dur": 189, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916909227, "dur": 148, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916909394, "dur": 6, "ph": "X", "name": "ProcessMessages 1795", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916909403, "dur": 99, "ph": "X", "name": "ReadAsync 1795", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916909564, "dur": 3, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916909570, "dur": 86, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916909658, "dur": 2, "ph": "X", "name": "ProcessMessages 2367", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916909660, "dur": 184, "ph": "X", "name": "ReadAsync 2367", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916909847, "dur": 210, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916910122, "dur": 34, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916910158, "dur": 1, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916910159, "dur": 148, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916910310, "dur": 120, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916910431, "dur": 1, "ph": "X", "name": "ProcessMessages 1704", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916910433, "dur": 179, "ph": "X", "name": "ReadAsync 1704", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916910678, "dur": 1156, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916911837, "dur": 227, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916912128, "dur": 110, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916912240, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916912242, "dur": 23, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916912267, "dur": 73, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916912341, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916912378, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916912448, "dur": 348, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916912798, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916912799, "dur": 57, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916912858, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916913062, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916913063, "dur": 117, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916913184, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916913212, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916913233, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916913442, "dur": 132, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916913651, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916913804, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916913831, "dur": 200, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916914065, "dur": 419, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916914567, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916914569, "dur": 78, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916914672, "dur": 6, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916914746, "dur": 382, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916915164, "dur": 4, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916915216, "dur": 188, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916915407, "dur": 16, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916915446, "dur": 124, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916915582, "dur": 204, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916915804, "dur": 4, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916915812, "dur": 117, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916916014, "dur": 199, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916916274, "dur": 61, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916916355, "dur": 56, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916916412, "dur": 13, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916916468, "dur": 99, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916916578, "dur": 21, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916916613, "dur": 103, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916916717, "dur": 18, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916916736, "dur": 61, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916916812, "dur": 92, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916917030, "dur": 39, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916917072, "dur": 64, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916917202, "dur": 120, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916917324, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916917325, "dur": 178, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916917523, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916917561, "dur": 254, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916917817, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916917819, "dur": 79, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916917901, "dur": 119, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916918029, "dur": 152, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916918196, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916918201, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916918232, "dur": 194, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916918433, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916918435, "dur": 77, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916918520, "dur": 195, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916918718, "dur": 40, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916918784, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916918915, "dur": 102, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919063, "dur": 89, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919155, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919197, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919255, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919291, "dur": 89, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919456, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919517, "dur": 7, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919525, "dur": 120, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919651, "dur": 69, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919722, "dur": 30, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919754, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919962, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916919964, "dur": 288, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916920273, "dur": 8126, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916928405, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916928408, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916928521, "dur": 1929, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916930452, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916930454, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916930543, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916930671, "dur": 2361, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916933035, "dur": 20435, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916953475, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916953478, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916953655, "dur": 2852, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916956512, "dur": 7334, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916963857, "dur": 11, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916963870, "dur": 720, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916964594, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916964596, "dur": 183, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916964783, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916964892, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916964959, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916965083, "dur": 268, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916965355, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916965358, "dur": 166, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916965527, "dur": 408, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916965938, "dur": 339, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916966279, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916966458, "dur": 364, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916966825, "dur": 353, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916967180, "dur": 149, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916967331, "dur": 380, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916967714, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916967797, "dur": 275, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916968077, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916968081, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916968212, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916968253, "dur": 331, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916968586, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916968613, "dur": 413, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916969030, "dur": 595, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916969628, "dur": 359, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916969989, "dur": 210, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916970201, "dur": 616, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916970820, "dur": 239, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916971062, "dur": 234, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916971299, "dur": 534, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916971836, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916972035, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916972040, "dur": 111, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916972154, "dur": 239, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916972396, "dur": 235, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916972634, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916972637, "dur": 694, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916973334, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916973540, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916973704, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916973811, "dur": 293, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916974106, "dur": 202, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916974325, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916974328, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916974432, "dur": 462, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916974895, "dur": 489, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916975387, "dur": 331, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916975964, "dur": 144, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916976131, "dur": 13, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916976147, "dur": 347, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916976498, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916976553, "dur": 86, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916976642, "dur": 138, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916976783, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916976785, "dur": 172, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916976959, "dur": 103, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916977067, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916977125, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916977126, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916977178, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916977271, "dur": 269, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916977877, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916977883, "dur": 405, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916978345, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916978390, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916978486, "dur": 248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916978736, "dur": 179, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916978916, "dur": 9, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916978926, "dur": 224, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916979153, "dur": 301, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916979457, "dur": 215, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916979731, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916979736, "dur": 152, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916979890, "dur": 622, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916980539, "dur": 10, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916980551, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916980625, "dur": 229, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916980856, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916980859, "dur": 308, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916981170, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916981197, "dur": 272, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916981471, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916981474, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916981570, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916981573, "dur": 120, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916981697, "dur": 416, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916982116, "dur": 160, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916982355, "dur": 527, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916982890, "dur": 175, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916983068, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916983070, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916983183, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916983273, "dur": 311, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916983587, "dur": 236, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916983839, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916983843, "dur": 548, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916984404, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916984408, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916984460, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916984508, "dur": 426, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916984936, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724916984939, "dur": 106936, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917091882, "dur": 5, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917091889, "dur": 100, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917091992, "dur": 151, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917092325, "dur": 140, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917092467, "dur": 189, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917092673, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917092721, "dur": 190, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917092913, "dur": 12019, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917104937, "dur": 313, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917105257, "dur": 9, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917105268, "dur": 1762, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917107033, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917107035, "dur": 412, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917107450, "dur": 413, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917107867, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917107870, "dur": 644, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917108517, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917108519, "dur": 365, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917108887, "dur": 241, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917109137, "dur": 1140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917110283, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917110286, "dur": 690, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917110981, "dur": 1355, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917112340, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917112429, "dur": 251, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917112683, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917112777, "dur": 327, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917113106, "dur": 1128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917114237, "dur": 372, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917114612, "dur": 478, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917115096, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917115100, "dur": 605, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917115708, "dur": 368, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917116078, "dur": 490, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917116571, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917116641, "dur": 556, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917117199, "dur": 1197, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118400, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118466, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118536, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118538, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118568, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118595, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118621, "dur": 60, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118683, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118713, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118810, "dur": 85, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118897, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917118992, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119071, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119138, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119224, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119291, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119355, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119403, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119427, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119490, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119527, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119602, "dur": 83, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119688, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119715, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119788, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119815, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119850, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917119940, "dur": 90, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120032, "dur": 85, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120119, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120168, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120212, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120311, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120347, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120380, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120404, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120444, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120517, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120586, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120613, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120638, "dur": 25, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120664, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120719, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120784, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120825, "dur": 52, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120879, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120906, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917120990, "dur": 55, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917121047, "dur": 329, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917121377, "dur": 147, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917121527, "dur": 335115, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917456650, "dur": 24, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917456676, "dur": 1489, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917458172, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917458201, "dur": 3248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917461453, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917461458, "dur": 308515, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770008, "dur": 17, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770026, "dur": 53, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770083, "dur": 59, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770152, "dur": 28, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770182, "dur": 63, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770248, "dur": 86, "ph": "X", "name": "ProcessMessages 8009", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770335, "dur": 59, "ph": "X", "name": "ReadAsync 8009", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770399, "dur": 160, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770569, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770572, "dur": 77, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770654, "dur": 57, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770713, "dur": 48, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770772, "dur": 26, "ph": "X", "name": "ProcessMessages 5299", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917770798, "dur": 7969, "ph": "X", "name": "ReadAsync 5299", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917778780, "dur": 3, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917778784, "dur": 377, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917779163, "dur": 28, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917779192, "dur": 336, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917779530, "dur": 697, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724917780230, "dur": 2804, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 74813, "tid": 1602569, "ts": 1748724917825117, "dur": 1398, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 74813, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 74813, "tid": 8589934592, "ts": 1748724916841780, "dur": 126803, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 74813, "tid": 8589934592, "ts": 1748724916968586, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 74813, "tid": 8589934592, "ts": 1748724916968593, "dur": 2738, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 74813, "tid": 1602569, "ts": 1748724917826517, "dur": 29, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 74813, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 74813, "tid": 4294967296, "ts": 1748724916740023, "dur": 1051060, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748724916751813, "dur": 82653, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748724917791446, "dur": 8050, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748724917795047, "dur": 218, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748724917799661, "dur": 23, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 74813, "tid": 1602569, "ts": 1748724917826547, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748724916862710, "dur": 3474, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724916866203, "dur": 19445, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724916885715, "dur": 77, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748724916885792, "dur": 91, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724916886393, "dur": 226, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748724916887389, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748724916891835, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748724916892698, "dur": 127, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cursor.Editor.ref.dll_CD3F41468EC5D7E9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748724916904843, "dur": 189, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748724916885889, "dur": 25532, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724916911430, "dur": 868553, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724917780000, "dur": 85, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724917780166, "dur": 91, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724917780299, "dur": 817, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748724916885845, "dur": 25591, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916911439, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748724916911665, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916912180, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916912307, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916912428, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916912496, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916912556, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916912748, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916912837, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916912929, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916913027, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916913209, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916913322, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916913503, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916913693, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916913774, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916913836, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916913895, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916913983, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916914102, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916914348, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916914444, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916914629, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916914718, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916914837, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916914964, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916915132, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724916915321, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916915409, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724916915615, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724916915913, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916916009, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724916916357, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916916470, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724916916619, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916916735, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916916904, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916917041, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724916917205, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916917314, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916917407, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724916917582, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916917698, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724916917852, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916917984, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748724916918196, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916918287, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724916918509, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916918615, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916918803, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916918884, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916919028, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916919144, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916919232, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916919339, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916919453, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916919580, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916919660, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916919752, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916919874, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916919989, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724916920169, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916920262, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916920447, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916920532, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916920650, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916920747, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916920847, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916921790, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916922652, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916923856, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916924949, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916926390, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916927701, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916928905, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916930153, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916931096, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916932032, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916932869, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916933656, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916934642, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916935453, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916936242, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916937291, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916938272, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916939400, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916940321, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916941310, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916942161, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916942959, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916943916, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916945054, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916945788, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916946698, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916947646, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916948447, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916949354, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916950818, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916951637, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916952494, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916953391, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916954253, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748724916954314, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916954395, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916955301, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916956339, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916957131, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916957967, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916959102, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916960564, "dur": 1999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916962564, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916963498, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916963744, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916964308, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916964789, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916965182, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916965530, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916965598, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916965678, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916966260, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916966918, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916967149, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916967634, "dur": 2382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916970016, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916970201, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916970274, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916971346, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916971738, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916971813, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916972555, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916972853, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916973194, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916974331, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916974597, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916974724, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916975835, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916975917, "dur": 1433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916977351, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916977650, "dur": 1949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916979599, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916979805, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916979863, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916979985, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916980911, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916981020, "dur": 2582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724916983603, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724916983672, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724916983978, "dur": 111340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724917095322, "dur": 2182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724917097528, "dur": 2643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724917100212, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724917102599, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724917102654, "dur": 2099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724917104753, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724917104812, "dur": 3217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724917108083, "dur": 2924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724917111055, "dur": 5376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724917116459, "dur": 5367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724917121852, "dur": 657542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724917779476, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916885847, "dur": 25608, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916911457, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748724916911680, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916911940, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916912110, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916912439, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916912610, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916912698, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916912845, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916912931, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916913038, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916913186, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916913342, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916913455, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916913580, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916913694, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916913820, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916913913, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916913972, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916914161, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916914344, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916914400, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916914605, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916914724, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916914914, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748724916915147, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916915265, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916915514, "dur": 13163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724916928678, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916929117, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916929187, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916929271, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916930401, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916931455, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916932328, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916933119, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916933991, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916934877, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916935757, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916936585, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916937572, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916938711, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916939781, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916940722, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916941592, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916942469, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916943308, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916944484, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916945344, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916946134, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916947114, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916948014, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916948785, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916950253, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916951182, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916951972, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916952857, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916953694, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916954694, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916955500, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916956539, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916957399, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916958322, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916959486, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916961045, "dur": 2268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916963373, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916963536, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916963722, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916964156, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916964533, "dur": 2533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724916967067, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916967370, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916967488, "dur": 3371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724916970860, "dur": 672, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916971536, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916972244, "dur": 600, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1748724916972845, "dur": 1846, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1748724916974691, "dur": 614, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 2, "ts": 1748724916972005, "dur": 3300, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916975306, "dur": 1409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724916976764, "dur": 5406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724916982170, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916982372, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916982451, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724916983234, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916983598, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916983666, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724916984004, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916984404, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916984595, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724916984959, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724916985116, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724916985264, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724916986504, "dur": 470706, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724917458213, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724917458812, "dur": 1430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724917461220, "dur": 343, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724917770250, "dur": 749, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724917463233, "dur": 307784, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724917779389, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748724917779472, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916885852, "dur": 25607, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916911461, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916912033, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916912124, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916912186, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916912247, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916912312, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916912373, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916912487, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916912582, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916912681, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916912793, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916912899, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916912986, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916913076, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916913243, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916913353, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916913486, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916913653, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916913815, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916913911, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916914018, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916914159, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916914305, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916914528, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916914622, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916914700, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916914829, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916914958, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916915172, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916915295, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748724916915536, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724916915854, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748724916916078, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916916173, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916916306, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916916396, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724916916587, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916916694, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916916834, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916916988, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916917147, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916917319, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916917411, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916917506, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916917624, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916917725, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916917821, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916917885, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916918057, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916918213, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916918322, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724916918421, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916918538, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916918606, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916918731, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916918832, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748724916919041, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916919148, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916919227, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916919331, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916919425, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916919537, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916919642, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916919720, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916919831, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724916919946, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916920078, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916920157, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916920268, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916920387, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916920552, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916920706, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916920785, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916920915, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916921897, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916922686, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916923944, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916924951, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916926377, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916927514, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916928744, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916929923, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916930929, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916931850, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916932733, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916933487, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916934467, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916935246, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916936107, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916937087, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916938019, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916939161, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916940158, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916941049, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916941940, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916942772, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916943726, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916944892, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916945635, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916946513, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916947483, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916948284, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916949077, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916950638, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916951477, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916952353, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916953200, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916954049, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916955039, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916956076, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916956941, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916957757, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916958838, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916960101, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916961723, "dur": 1875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916963719, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916964154, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916965247, "dur": 4511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724916969758, "dur": 674, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916970470, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916970597, "dur": 1370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724916971968, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916972512, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916972613, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916972722, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916973080, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724916973733, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916974236, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916974332, "dur": 1060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916975432, "dur": 3967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724916979400, "dur": 611, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916980018, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916980122, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916980361, "dur": 2365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724916982727, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916982920, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916983001, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724916983452, "dur": 904, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916984378, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724916984515, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724916984936, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724916985090, "dur": 110126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724917095219, "dur": 2094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748724917097358, "dur": 3570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748724917100928, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724917100985, "dur": 3904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748724917104931, "dur": 3176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748724917108151, "dur": 2084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748724917110278, "dur": 4734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748724917115013, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724917115093, "dur": 2870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748724917117964, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724917118025, "dur": 4289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748724917122331, "dur": 657733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916885859, "dur": 25609, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916911470, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916911942, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916912105, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916912167, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916912230, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916912346, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916912455, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916912539, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916912728, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916912790, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916912920, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916912982, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916913126, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916913229, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916913395, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916913468, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916913607, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916913709, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916913846, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916913929, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916914049, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916914204, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916914367, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916914502, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916914564, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748724916914848, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916914924, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916915435, "dur": 15514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916930950, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916931195, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916931245, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916931342, "dur": 2405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916933787, "dur": 20136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916953924, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916954219, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916954353, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916954424, "dur": 2790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916957255, "dur": 6630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916963886, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916964082, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916964158, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916965161, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916965622, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916965726, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916966312, "dur": 1024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916967337, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916967831, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916968046, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916968115, "dur": 4276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916972392, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916972697, "dur": 1764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916974462, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916974766, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916975477, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916975760, "dur": 1138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916976898, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916977153, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916977325, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724916977470, "dur": 3220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916980690, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916980769, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916981286, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916981356, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724916981790, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916981891, "dur": 1796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724916983687, "dur": 111549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724917095256, "dur": 3451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724917098760, "dur": 2859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724917101620, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724917101679, "dur": 4223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724917105903, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724917105958, "dur": 2660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724917108668, "dur": 4569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724917113237, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724917113303, "dur": 2010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724917115314, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724917115379, "dur": 2456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724917117853, "dur": 4305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724917122180, "dur": 657874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916885865, "dur": 25641, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916911508, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916912052, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916912435, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916912505, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916912578, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916912755, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916912851, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916912934, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916913001, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916913194, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916913299, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916913511, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916913673, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916913755, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916913812, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916913872, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916913949, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916914083, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916914303, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916914534, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748724916914843, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916914945, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724916915139, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916915248, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724916915405, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_C5A9EDBF39CB7C6A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916915486, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724916915602, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724916915950, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748724916916130, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916916229, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916916317, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916916443, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724916916664, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916916788, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916916972, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916917069, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724916917211, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916917309, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724916917380, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724916917586, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916917700, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916917808, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916917911, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916918104, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916918255, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916918346, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916918430, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916918535, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916918646, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916918806, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916918894, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916918973, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916919074, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916919178, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916919264, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916919385, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916919495, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916919605, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916919692, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916919799, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916919886, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916919992, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916920094, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916920198, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916920280, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916920452, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916920538, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916920662, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916920760, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916920854, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916922185, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916923080, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916924262, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916925331, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916926872, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916928245, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916929209, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916930375, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916931452, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916932283, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916933112, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916933994, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916934886, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916935763, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916936597, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916937577, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916938733, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916939832, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916940774, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916941640, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916942530, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916943379, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916944568, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916945392, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916946216, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916947154, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916948010, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916948788, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916950263, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916951206, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916951988, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916952898, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916953710, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916954706, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916955534, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916956572, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916957426, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916958348, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916959484, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916961029, "dur": 2180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916963210, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916963737, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916964173, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916964423, "dur": 1329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724916965753, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916965865, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916966110, "dur": 1765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724916967876, "dur": 597, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916968527, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916969001, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916969817, "dur": 2680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724916972497, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916972768, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916973358, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916973427, "dur": 1470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724916974898, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916975135, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916975259, "dur": 1426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724916976685, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916977105, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_C4A720C3D568CE76.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916977269, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916977755, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724916978567, "dur": 689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916979265, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916979482, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748724916979545, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916979683, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748724916979752, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916979845, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748724916979948, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916980043, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748724916980323, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916980832, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724916981498, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916981581, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916981648, "dur": 2035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916983683, "dur": 1502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724916985188, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724916985314, "dur": 110033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724917095352, "dur": 2828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724917098232, "dur": 2876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724917101146, "dur": 3153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724917104339, "dur": 2748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724917107121, "dur": 2611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724917109775, "dur": 2000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724917111808, "dur": 1886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724917113695, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724917113768, "dur": 2881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724917116650, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724917116715, "dur": 4428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724917121144, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724917121352, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724917121465, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724917121543, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724917121747, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724917121856, "dur": 657639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724917779549, "dur": 387, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748724917779937, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916885871, "dur": 25648, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916911526, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916912096, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916912195, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916912319, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916912443, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916912516, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916912634, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916912747, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916912817, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916912927, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916912994, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916913173, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916913252, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916913409, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916913562, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916913675, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916913763, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916913816, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916913886, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916913972, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916914126, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916914308, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916914472, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916914613, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916914738, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916914880, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916914990, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916915207, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916915320, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748724916915612, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748724916915814, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748724916916065, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916916180, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916916305, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916916395, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916916484, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748724916916687, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916916844, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916916998, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748724916917223, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916917393, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916917480, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916917551, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916917681, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916917793, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916917844, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916917958, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916918222, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916918411, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916918527, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916918627, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916918745, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748724916918941, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916919125, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916919209, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916919295, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916919395, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916919507, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748724916919697, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916919784, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916919908, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916920016, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916920099, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916920188, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916920303, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916920468, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916920625, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916920751, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916920842, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916920968, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916921874, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916922688, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916923931, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916924953, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916926369, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916927667, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916928881, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916930120, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916931055, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916931971, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916932790, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916933550, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916934521, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916935311, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916936146, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916937134, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916938089, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916939201, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916940206, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916941102, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916942025, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916942847, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916943807, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916944949, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916945690, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916946620, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916947572, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916948383, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916949181, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916950736, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916951568, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916952411, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916953284, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916954137, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916955119, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916956149, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916956983, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916957810, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916958883, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916960303, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916961822, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916963307, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916963727, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916964169, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916964959, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724916965762, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916965914, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916966021, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916966155, "dur": 1898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724916968053, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916968627, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916968751, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916969270, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916969490, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916969581, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916969663, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724916970773, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916970900, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916971367, "dur": 4580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724916975948, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916976123, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916976217, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916976382, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916976444, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916976528, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916976628, "dur": 5455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724916982083, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916982167, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724916982252, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724916982882, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724916983689, "dur": 111563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917095257, "dur": 3026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724917098285, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917098353, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724917100654, "dur": 2530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724917103194, "dur": 3043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724917106238, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917106307, "dur": 3074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724917109418, "dur": 2290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724917111743, "dur": 1776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724917113576, "dur": 3630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724917117248, "dur": 2622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724917119870, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917120626, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917120786, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917121084, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917121262, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917121392, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748724917121451, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917121578, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748724917121630, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917121804, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724917122334, "dur": 657736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916885878, "dur": 25665, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916911545, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916912062, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916912155, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916912282, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916912491, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916912544, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916912774, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916912969, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916913118, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916913245, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916913414, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916913495, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916913677, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916913767, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916913878, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916914000, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916914139, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916914301, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916914436, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916914640, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916914706, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916914777, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916914897, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916915032, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916915208, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916915340, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748724916915641, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748724916916161, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_18DD15F206BB0EF4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916916335, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748724916916463, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916916566, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916916653, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916916766, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916916959, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916917060, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916917166, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748724916917346, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916917399, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748724916917513, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916917634, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916917778, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916917837, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916917945, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916918133, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916918263, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916918406, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916918507, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916918580, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916918695, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916918796, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916918867, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916919048, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916919153, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916919222, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916919332, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916919429, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916919543, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916919644, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916919715, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916919832, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916919932, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916920082, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916920168, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916920258, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916920406, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916920525, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916920632, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916920742, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916920818, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916920952, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916921852, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916922684, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916923940, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916924985, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916926394, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916927706, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916928914, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916930124, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916931078, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916932042, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916932907, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916933659, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916934646, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916935444, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916936249, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916937298, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916938339, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916939470, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916940363, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916941340, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916942225, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916943048, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916943981, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916945132, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916945842, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916946775, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916947734, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916948525, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916949625, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916950960, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916951728, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916952611, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916953479, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916954470, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916955312, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916956353, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916957148, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916957990, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916959166, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916960589, "dur": 2004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916962594, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916963427, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916963757, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916964339, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916964398, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916964478, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724916965257, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916965579, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916965665, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916966244, "dur": 1315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724916967559, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916967802, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916967906, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916967973, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916968077, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916968446, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916968811, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916968951, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916969252, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916969307, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916969405, "dur": 2299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724916971704, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916972118, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724916972754, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916973024, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916973200, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916973261, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724916973969, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916974124, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_E30EE1A78470D274.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916974262, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916974419, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916974902, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916974968, "dur": 1753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724916976721, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916977128, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916978126, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916978206, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916978296, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916978383, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724916978581, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916978686, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724916978990, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916979136, "dur": 1241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724916980377, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916980499, "dur": 3175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724916983676, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724916983794, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724916984068, "dur": 111105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724917095177, "dur": 2045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917097280, "dur": 2408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917099730, "dur": 3192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917102976, "dur": 2064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917105040, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724917105245, "dur": 3232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917108519, "dur": 3141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917111700, "dur": 2967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917114668, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724917114722, "dur": 2644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917117419, "dur": 4276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917121751, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724917121815, "dur": 336399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724917458445, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748724917458216, "dur": 2338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917461631, "dur": 526, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724917770284, "dur": 1250, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724917463231, "dur": 308316, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724917779491, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748724917779487, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748724917779551, "dur": 409, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748724916885884, "dur": 25670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916911554, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916912019, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916912160, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916912222, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916912338, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916912447, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916912518, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916912648, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916912762, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916912866, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916913016, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916913150, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916913286, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916913392, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916913493, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916913605, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916913808, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916913944, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916914126, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916914229, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916914469, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916914587, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724916914853, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916914956, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916915176, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916915280, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724916915452, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724916915674, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748724916915920, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916915993, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724916916133, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916916197, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916916304, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916916386, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916916552, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748724916916712, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916916869, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916917025, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916917113, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724916917267, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916917504, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916917597, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916917738, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916917829, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916917919, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916918122, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916918225, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916918338, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916918450, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916918603, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916918714, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916918823, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916918894, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916919015, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916919129, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916919212, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916919306, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916919402, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916919514, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916919598, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916919700, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916919796, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916919881, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916920043, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916920116, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916920243, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916920332, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916920502, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916920635, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916920747, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916920852, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916921009, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916921908, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916922745, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916923994, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916925017, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916926417, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916927742, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916928954, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916930226, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916931177, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916932106, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916932955, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916933721, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916934694, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916935556, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916936319, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916937341, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916938384, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916939535, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916940420, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916941374, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916942229, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916943026, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916943968, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916945114, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916945833, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916946738, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916947689, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916948486, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916949460, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916950875, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916951669, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916952520, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916953428, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916954384, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916954461, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916955314, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916956343, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916957128, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916957946, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916959067, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916960522, "dur": 1959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916962482, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916963379, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916963714, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916964150, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916964485, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916964558, "dur": 1596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748724916966155, "dur": 473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916966660, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916966770, "dur": 1910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748724916968681, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916969085, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724916969208, "dur": 1545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748724916970754, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916970981, "dur": 2798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1748724916973779, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916974170, "dur": 1168, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917092481, "dur": 1088, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724916975697, "dur": 117892, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1748724917095171, "dur": 3772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724917098944, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917099007, "dur": 3279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724917102337, "dur": 3038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724917105422, "dur": 3714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724917109178, "dur": 3950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724917113182, "dur": 2209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724917115445, "dur": 3659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724917119105, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917119943, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917120208, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917120337, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917120471, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917120716, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917120877, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917121157, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917121350, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917121584, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917121761, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724917122186, "dur": 657791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724917782725, "dur": 669, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 74813, "tid": 1602569, "ts": 1748724917833554, "dur": 5614, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 74813, "tid": 1602569, "ts": 1748724917839220, "dur": 4303, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 74813, "tid": 1602569, "ts": 1748724917823582, "dur": 25298, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}