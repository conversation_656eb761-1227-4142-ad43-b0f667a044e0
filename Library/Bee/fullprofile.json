{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 74813, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 74813, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 74813, "tid": 1533069, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 74813, "tid": 1533069, "ts": 1748724017713221, "dur": 411, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 74813, "tid": 1533069, "ts": 1748724017717772, "dur": 1243, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 74813, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 74813, "tid": 1, "ts": 1748724016135617, "dur": 28600, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 74813, "tid": 1, "ts": 1748724016164225, "dur": 236247, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 74813, "tid": 1, "ts": 1748724016400482, "dur": 597339, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 74813, "tid": 1533069, "ts": 1748724017719026, "dur": 50, "ph": "X", "name": "", "args": {}}, {"pid": 74813, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016131137, "dur": 14099, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016145239, "dur": 1557082, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016152708, "dur": 8358, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016161071, "dur": 2564, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016163637, "dur": 15747, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016179388, "dur": 387, "ph": "X", "name": "ProcessMessages 1778", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016180082, "dur": 34, "ph": "X", "name": "ReadAsync 1778", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016180121, "dur": 2, "ph": "X", "name": "ProcessMessages 2856", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016180125, "dur": 32789, "ph": "X", "name": "ReadAsync 2856", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016212920, "dur": 72223, "ph": "X", "name": "ProcessMessages 8150", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016285148, "dur": 81983, "ph": "X", "name": "ReadAsync 8150", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367136, "dur": 7, "ph": "X", "name": "ProcessMessages 8115", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367144, "dur": 63, "ph": "X", "name": "ReadAsync 8115", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367208, "dur": 5, "ph": "X", "name": "ProcessMessages 8151", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367214, "dur": 15, "ph": "X", "name": "ReadAsync 8151", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367232, "dur": 20, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367254, "dur": 292, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367548, "dur": 4, "ph": "X", "name": "ProcessMessages 6824", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367554, "dur": 22, "ph": "X", "name": "ReadAsync 6824", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367576, "dur": 1, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367578, "dur": 11, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367591, "dur": 10, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367603, "dur": 11, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367616, "dur": 191, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367808, "dur": 1, "ph": "X", "name": "ProcessMessages 1428", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016367810, "dur": 221, "ph": "X", "name": "ReadAsync 1428", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368035, "dur": 432, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368469, "dur": 1, "ph": "X", "name": "ProcessMessages 1070", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368470, "dur": 15, "ph": "X", "name": "ReadAsync 1070", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368487, "dur": 12, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368501, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368523, "dur": 183, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368707, "dur": 2, "ph": "X", "name": "ProcessMessages 1861", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368710, "dur": 10, "ph": "X", "name": "ReadAsync 1861", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368722, "dur": 12, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368736, "dur": 52, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368789, "dur": 1, "ph": "X", "name": "ProcessMessages 1061", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368790, "dur": 66, "ph": "X", "name": "ReadAsync 1061", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368858, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368859, "dur": 11, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016368872, "dur": 163, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369037, "dur": 16, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369055, "dur": 15, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369074, "dur": 13, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369089, "dur": 97, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369188, "dur": 19, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369209, "dur": 31, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369242, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369246, "dur": 244, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369492, "dur": 5, "ph": "X", "name": "ProcessMessages 5240", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369498, "dur": 14, "ph": "X", "name": "ReadAsync 5240", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369514, "dur": 49, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369565, "dur": 11, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369578, "dur": 34, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369613, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369614, "dur": 13, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369629, "dur": 59, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369690, "dur": 11, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016369703, "dur": 389, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370094, "dur": 3, "ph": "X", "name": "ProcessMessages 5240", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370098, "dur": 37, "ph": "X", "name": "ReadAsync 5240", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370137, "dur": 12, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370152, "dur": 10, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370164, "dur": 30, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370196, "dur": 18, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370220, "dur": 10, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370232, "dur": 11, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370416, "dur": 18, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370435, "dur": 3, "ph": "X", "name": "ProcessMessages 3936", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370439, "dur": 11, "ph": "X", "name": "ReadAsync 3936", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370451, "dur": 5, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370457, "dur": 11, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370470, "dur": 335, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370806, "dur": 3, "ph": "X", "name": "ProcessMessages 4579", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370810, "dur": 13, "ph": "X", "name": "ReadAsync 4579", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370826, "dur": 11, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016370838, "dur": 203, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371043, "dur": 3, "ph": "X", "name": "ProcessMessages 3985", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371047, "dur": 10, "ph": "X", "name": "ReadAsync 3985", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371059, "dur": 11, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371072, "dur": 10, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371084, "dur": 10, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371096, "dur": 206, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371304, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371320, "dur": 3, "ph": "X", "name": "ProcessMessages 4664", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371323, "dur": 320, "ph": "X", "name": "ReadAsync 4664", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371645, "dur": 5, "ph": "X", "name": "ProcessMessages 8166", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371651, "dur": 12, "ph": "X", "name": "ReadAsync 8166", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371665, "dur": 10, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371677, "dur": 16, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371698, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371720, "dur": 26, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371747, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371748, "dur": 11, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371761, "dur": 19, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371782, "dur": 12, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016371796, "dur": 208, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372005, "dur": 3, "ph": "X", "name": "ProcessMessages 5006", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372009, "dur": 11, "ph": "X", "name": "ReadAsync 5006", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372022, "dur": 11, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372035, "dur": 172, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372208, "dur": 2, "ph": "X", "name": "ProcessMessages 3369", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372211, "dur": 11, "ph": "X", "name": "ReadAsync 3369", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372223, "dur": 10, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372235, "dur": 24, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372261, "dur": 10, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372273, "dur": 11, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372285, "dur": 188, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372475, "dur": 3, "ph": "X", "name": "ProcessMessages 4587", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372479, "dur": 320, "ph": "X", "name": "ReadAsync 4587", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372800, "dur": 5, "ph": "X", "name": "ProcessMessages 8150", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372806, "dur": 13, "ph": "X", "name": "ReadAsync 8150", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016372821, "dur": 375, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373197, "dur": 5, "ph": "X", "name": "ProcessMessages 6601", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373440, "dur": 18, "ph": "X", "name": "ReadAsync 6601", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373458, "dur": 4, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373464, "dur": 21, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373486, "dur": 10, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373498, "dur": 12, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373511, "dur": 11, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373524, "dur": 31, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373556, "dur": 1, "ph": "X", "name": "ProcessMessages 1145", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373557, "dur": 10, "ph": "X", "name": "ReadAsync 1145", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373569, "dur": 16, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373588, "dur": 11, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373601, "dur": 19, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373622, "dur": 13, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373637, "dur": 11, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373657, "dur": 12, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373671, "dur": 13, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373686, "dur": 20, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373708, "dur": 12, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373721, "dur": 13, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373735, "dur": 4, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373740, "dur": 11, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373753, "dur": 11, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373766, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373783, "dur": 13, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373798, "dur": 11, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016373811, "dur": 317, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016374129, "dur": 2, "ph": "X", "name": "ProcessMessages 3882", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016374132, "dur": 102, "ph": "X", "name": "ReadAsync 3882", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016374237, "dur": 13, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016374252, "dur": 11, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016374265, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016374287, "dur": 439, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016374727, "dur": 4, "ph": "X", "name": "ProcessMessages 5432", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016374732, "dur": 93, "ph": "X", "name": "ReadAsync 5432", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016374828, "dur": 192, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016375022, "dur": 39, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016375063, "dur": 756, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016375822, "dur": 607, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016376432, "dur": 350, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016376783, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016376785, "dur": 755, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016377542, "dur": 156, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016377701, "dur": 323, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016378027, "dur": 137, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016378166, "dur": 220, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016378388, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016378390, "dur": 127, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016378518, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016378519, "dur": 220, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016378741, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016378742, "dur": 2130, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016380888, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016380890, "dur": 15, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016380907, "dur": 5315, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016386225, "dur": 2, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016386228, "dur": 59, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016386290, "dur": 512, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016386804, "dur": 1, "ph": "X", "name": "ProcessMessages 1370", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016386806, "dur": 32, "ph": "X", "name": "ReadAsync 1370", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016386841, "dur": 1, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016386842, "dur": 54, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016386898, "dur": 43, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016386943, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016386945, "dur": 534, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016387479, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016387481, "dur": 25, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016387508, "dur": 118, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016387629, "dur": 3169, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016390801, "dur": 7, "ph": "X", "name": "ProcessMessages 8186", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016390818, "dur": 22, "ph": "X", "name": "ReadAsync 8186", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016390841, "dur": 2, "ph": "X", "name": "ProcessMessages 2508", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016390844, "dur": 33, "ph": "X", "name": "ReadAsync 2508", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016390881, "dur": 1, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016390882, "dur": 34, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016390918, "dur": 1, "ph": "X", "name": "ProcessMessages 1107", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016390919, "dur": 214, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016391135, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016391136, "dur": 22, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016391161, "dur": 220, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016391383, "dur": 363, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016391747, "dur": 1, "ph": "X", "name": "ProcessMessages 1363", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016394127, "dur": 41, "ph": "X", "name": "ReadAsync 1363", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016394169, "dur": 6, "ph": "X", "name": "ProcessMessages 8158", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016394176, "dur": 23, "ph": "X", "name": "ReadAsync 8158", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016394201, "dur": 156, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016394359, "dur": 47, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016394409, "dur": 206, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016394617, "dur": 28, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016394649, "dur": 208, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016394859, "dur": 189, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016395051, "dur": 329, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016395381, "dur": 1, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016395383, "dur": 116, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016395501, "dur": 386, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016395898, "dur": 19, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016395919, "dur": 408, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016396349, "dur": 1, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016396351, "dur": 176, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016396529, "dur": 189, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016396720, "dur": 40, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016396761, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016396762, "dur": 320, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397084, "dur": 1, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397086, "dur": 124, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397213, "dur": 17, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397232, "dur": 372, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397606, "dur": 23, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397632, "dur": 46, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397689, "dur": 173, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397865, "dur": 20, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397888, "dur": 20, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397910, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016397932, "dur": 230, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016398163, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016398165, "dur": 216, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016398383, "dur": 173, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016398558, "dur": 156, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016398716, "dur": 359, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016399077, "dur": 1, "ph": "X", "name": "ProcessMessages 1172", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016399079, "dur": 267, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016399347, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016399349, "dur": 337, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016399687, "dur": 3, "ph": "X", "name": "ProcessMessages 3942", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016399700, "dur": 61, "ph": "X", "name": "ReadAsync 3942", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016399763, "dur": 211, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016399976, "dur": 41, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016400020, "dur": 416, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016400440, "dur": 19, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016400460, "dur": 2, "ph": "X", "name": "ProcessMessages 1863", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016400462, "dur": 20, "ph": "X", "name": "ReadAsync 1863", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016400668, "dur": 25, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016400694, "dur": 1, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016400696, "dur": 254, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016400951, "dur": 1, "ph": "X", "name": "ProcessMessages 994", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016400953, "dur": 348, "ph": "X", "name": "ReadAsync 994", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016401303, "dur": 3, "ph": "X", "name": "ProcessMessages 3764", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016401307, "dur": 148, "ph": "X", "name": "ReadAsync 3764", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016401457, "dur": 258, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016401717, "dur": 163, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016401881, "dur": 184, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016402067, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016402069, "dur": 708, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016402778, "dur": 8, "ph": "X", "name": "ProcessMessages 1488", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016402787, "dur": 544, "ph": "X", "name": "ReadAsync 1488", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016403332, "dur": 3, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016403336, "dur": 92, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016403431, "dur": 395, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016403827, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016403829, "dur": 90, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016403976, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016404050, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016404052, "dur": 21, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016404075, "dur": 678, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016404755, "dur": 8, "ph": "X", "name": "ProcessMessages 1600", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016404764, "dur": 42, "ph": "X", "name": "ReadAsync 1600", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405033, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405035, "dur": 33, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405070, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405073, "dur": 52, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405127, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405466, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405498, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405701, "dur": 25, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405727, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405729, "dur": 37, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405769, "dur": 221, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405991, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016405994, "dur": 31, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016406028, "dur": 26, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016406056, "dur": 53, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016406111, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016406187, "dur": 437, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016406626, "dur": 34542, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016441172, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016441175, "dur": 265, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016441443, "dur": 129, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016441575, "dur": 211, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016441809, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016441811, "dur": 1091, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016442905, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016442935, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016442937, "dur": 1766, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016444704, "dur": 3, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016444707, "dur": 167, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016444881, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016444887, "dur": 5912, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016450802, "dur": 669, "ph": "X", "name": "ProcessMessages 1124", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016451597, "dur": 108, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016451708, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016452087, "dur": 36, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016452154, "dur": 409, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016452572, "dur": 6, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016452579, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016452643, "dur": 498, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016453143, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016453145, "dur": 336, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016453491, "dur": 9932, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016463427, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016463433, "dur": 126633, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016590072, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724016590464, "dur": 411101, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017001581, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017001584, "dur": 251, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017021527, "dur": 171, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017021699, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017021701, "dur": 24, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017021727, "dur": 21, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017021750, "dur": 450, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017022203, "dur": 20303, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017042932, "dur": 3904, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017046843, "dur": 15, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017047229, "dur": 109, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017047340, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017047342, "dur": 7329, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017055872, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017055876, "dur": 63, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017055941, "dur": 9, "ph": "X", "name": "ProcessMessages 1760", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017055952, "dur": 228743, "ph": "X", "name": "ReadAsync 1760", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017284704, "dur": 26, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017284731, "dur": 1665, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017286400, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017286402, "dur": 2407, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017288817, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017288821, "dur": 398028, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017686862, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017686866, "dur": 24, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017686894, "dur": 44, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017686943, "dur": 21, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017686966, "dur": 26, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017686994, "dur": 39, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017687034, "dur": 31, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017687068, "dur": 25, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017687100, "dur": 21, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017687123, "dur": 25, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017687155, "dur": 36, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017687191, "dur": 12, "ph": "X", "name": "ProcessMessages 5498", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017687204, "dur": 8405, "ph": "X", "name": "ReadAsync 5498", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017695612, "dur": 2, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017695615, "dur": 935, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017696557, "dur": 27, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017696584, "dur": 178, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017696765, "dur": 252, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748724017697019, "dur": 5246, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 74813, "tid": 1533069, "ts": 1748724017719078, "dur": 678, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 74813, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 74813, "tid": 8589934592, "ts": 1748724016124621, "dur": 873256, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 74813, "tid": 8589934592, "ts": 1748724016997881, "dur": 11, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 74813, "tid": 8589934592, "ts": 1748724016997893, "dur": 3805, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 74813, "tid": 1533069, "ts": 1748724017719758, "dur": 16, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 74813, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 74813, "tid": 4294967296, "ts": 1748724015846982, "dur": 1856748, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748724015858550, "dur": 239907, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748724017703965, "dur": 4537, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748724017707197, "dur": 56, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748724017708580, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 74813, "tid": 1533069, "ts": 1748724017719783, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748724016139705, "dur": 5513, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724016145225, "dur": 33815, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724016179087, "dur": 75, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748724016179163, "dur": 105, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724016208362, "dur": 4542, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748724016213215, "dur": 74424, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748724016287961, "dur": 79239, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748724016372726, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748724016390080, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748724016393767, "dur": 384, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748724016179274, "dur": 222150, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724016401432, "dur": 1295050, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724017696513, "dur": 50, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724017696588, "dur": 61, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724017696681, "dur": 771, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748724016179233, "dur": 222225, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016401461, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016401954, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016402053, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016402293, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016402667, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016402726, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016402982, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748724016403075, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724016403238, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748724016403548, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724016404019, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724016404090, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724016404504, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724016404668, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724016404999, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016405149, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016405386, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016405469, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748724016405587, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016405658, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016405728, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016405816, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016405920, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016406017, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016406138, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016406890, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016407512, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016408190, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016408852, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016409623, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016410318, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016411113, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016411823, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016412496, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016413190, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016414543, "dur": 2148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016416691, "dur": 2396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016419087, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016420340, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016421360, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016422228, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016423163, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016424150, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016425030, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016425789, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016426541, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016427411, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016427998, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016428691, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016429317, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016429915, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016430670, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016431497, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016432087, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016432733, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016433283, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016433835, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016434386, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016434936, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016435548, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016436137, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016436898, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016437540, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016438161, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016438791, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016438924, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016439136, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016439265, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016439319, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016439395, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016439839, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016439936, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724016440807, "dur": 725, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016441613, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016441995, "dur": 1312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724016443308, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016443791, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016444018, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724016445245, "dur": 868, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016446133, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016446207, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724016447073, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016447371, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016447673, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724016448649, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016449153, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016449255, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016449353, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724016450446, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016450557, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.Shared.ref.dll_0A54E06D0D53005C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016450753, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016450916, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_94629FB7B2A83F68.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016450998, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016451107, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016451245, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748724016451492, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748724016452303, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016452429, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016452550, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016452710, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016452788, "dur": 1158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724016453946, "dur": 547605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017001977, "dur": 5115, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1748724017007093, "dur": 5241, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1748724017012334, "dur": 583, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1748724017001551, "dur": 11372, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017012923, "dur": 9950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017022873, "dur": 2454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724017025327, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017025427, "dur": 3781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724017029209, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017029450, "dur": 3264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724017032748, "dur": 3298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724017036048, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017036151, "dur": 2437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724017038588, "dur": 872, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017039474, "dur": 2580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724017042055, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017042239, "dur": 3144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724017045391, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017045455, "dur": 4387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724017049843, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017050113, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017050173, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748724017050223, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017050357, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017050455, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017050688, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017050802, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017050856, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cursor.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748724017050911, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017051113, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017051253, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017051390, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017051579, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017051795, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017052018, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017052085, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017052326, "dur": 233206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017285759, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748724017285540, "dur": 1966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724017288268, "dur": 277, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017686604, "dur": 321, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748724017289089, "dur": 397853, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748724017695465, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748724017695453, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748724017695544, "dur": 904, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748724016179229, "dur": 222220, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016401453, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748724016401670, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016402018, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016402346, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016402670, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016402729, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016402824, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748724016403074, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016403128, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748724016403541, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748724016403692, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748724016404015, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748724016404107, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748724016404377, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748724016404542, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748724016404760, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748724016404940, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016405015, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016405178, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016405344, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016405408, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016405493, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016405691, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016405804, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016405911, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016405971, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748724016406089, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016406161, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016406901, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016407525, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016408223, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016408870, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016409642, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016410334, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016411133, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016411846, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016412514, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016413203, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016414541, "dur": 2281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016416822, "dur": 2364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016419186, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016420359, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016421398, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016422256, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016423257, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016424253, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016425077, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016425842, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016426646, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016427454, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016428036, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016428722, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016429347, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016429950, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016430743, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016431552, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016432139, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016432789, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016433330, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016433880, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016434434, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016434981, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016435590, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016436182, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016436938, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016437584, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016438199, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016438834, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016438970, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016439115, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016439303, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016439390, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016439885, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016440685, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724016441177, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016441793, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016442130, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724016442566, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016443143, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016443230, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016443411, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016443653, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016443827, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016444032, "dur": 966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724016444998, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016445171, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724016446826, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016447432, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016447619, "dur": 1614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724016449234, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016449695, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016449832, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724016450428, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016450883, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_BC6ADFF987BC82A3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016450944, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016451026, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748724016451329, "dur": 993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748724016452323, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016452766, "dur": 1194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724016453960, "dur": 568893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724017022857, "dur": 2205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724017025063, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724017025141, "dur": 2782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724017027967, "dur": 3867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724017031886, "dur": 1960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724017033846, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724017033906, "dur": 2037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724017035944, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724017036195, "dur": 2217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724017038413, "dur": 924, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724017039343, "dur": 3532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724017042875, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724017042939, "dur": 2141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724017045119, "dur": 7104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748724017052224, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724017052307, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748724017052822, "dur": 643642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016179228, "dur": 222215, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016401447, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748724016401673, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016401952, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016402342, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016402674, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016402729, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016402966, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724016403076, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748724016403380, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724016403610, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748724016403875, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016403929, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724016404088, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724016404364, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724016404590, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724016404780, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748724016404848, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748724016404980, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016405059, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016405205, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016405261, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016405358, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016405423, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016405530, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016405686, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016405773, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016405824, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016405927, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016406027, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748724016406092, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016406165, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016406915, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016407537, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016408242, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016408889, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016409651, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016410339, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016411120, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016411836, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016412507, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016413201, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016414553, "dur": 2308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016416862, "dur": 2302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016419164, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016420356, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016421362, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016422240, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016423165, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016424132, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016425017, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016425776, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016426489, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016427398, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016427982, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016428676, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016429294, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016429895, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016430660, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016431499, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016432072, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016432719, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016433269, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016433817, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016434367, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016434927, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016435537, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016436134, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016436884, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016437521, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016438133, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016438752, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016438989, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016439174, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016439247, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016439309, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016439393, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016439882, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016440177, "dur": 825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724016441002, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016441237, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016441365, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724016442406, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016442848, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016443357, "dur": 1733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724016445092, "dur": 1093, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016446188, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016446660, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724016446968, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016447279, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_8B099D8C97AF393F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016447339, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016447391, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724016448086, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016448388, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724016448862, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016449017, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016449346, "dur": 3678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724016453024, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016453281, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016453349, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724016453765, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016453929, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748724016454014, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724016454238, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724016454764, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724016462028, "dur": 822435, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724017285529, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748724017286270, "dur": 1350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748724017288417, "dur": 243, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724017686615, "dur": 486, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748724017289096, "dur": 398025, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748724017695523, "dur": 945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016179242, "dur": 222221, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016401466, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724016402336, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724016402694, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724016402973, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748724016403446, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748724016403702, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748724016404013, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748724016404180, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748724016404462, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748724016404775, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748724016404871, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748724016404938, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016404992, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016405104, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016405370, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016405442, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016405579, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016405683, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016405769, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748724016405923, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016406099, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016406867, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016407496, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016408163, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016408827, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016409585, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016410291, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016411090, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016411804, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016412475, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016413166, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016414376, "dur": 2292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016416669, "dur": 2369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016419040, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016420335, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016421353, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016422225, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016423183, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016424176, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016425055, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016425824, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016426603, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016427430, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016428014, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016428711, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016429331, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016429931, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016430710, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016431535, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016432120, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016432774, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016433321, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016433868, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016434422, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016434975, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016435586, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016436174, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016436952, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016437595, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016438212, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016438827, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016439029, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016439244, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016439334, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016439386, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016439882, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724016440138, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016440198, "dur": 1156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724016441354, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016441868, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724016442033, "dur": 2288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724016444321, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016444578, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724016444702, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724016444817, "dur": 1018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724016445835, "dur": 661, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016446539, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724016446631, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724016447047, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016447275, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_697AC546F729FDEE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724016447375, "dur": 1073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724016448448, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016448712, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748724016448900, "dur": 1240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724016450141, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016450366, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724016451462, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016451717, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748724016452163, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016452490, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016452605, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016452755, "dur": 1207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724016453962, "dur": 568936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724017022902, "dur": 3783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724017026736, "dur": 5760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724017032498, "dur": 931, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724017033434, "dur": 3373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724017036808, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724017037201, "dur": 1980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724017039182, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724017039440, "dur": 1834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724017041274, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724017041531, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724017043816, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724017043956, "dur": 2858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724017046814, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748724017046880, "dur": 5566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748724017052474, "dur": 644012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016179248, "dur": 222220, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016401473, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016401995, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016402200, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016402679, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016402754, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016402816, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748724016403071, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724016403212, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748724016403519, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724016403876, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016403940, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724016404417, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748724016404817, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748724016405093, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016405231, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016405352, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016405415, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016405508, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016405590, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016405651, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016405712, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016405846, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016405901, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016405963, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016406052, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016407103, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016407744, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016408456, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016409116, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016409876, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016410619, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016411355, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016412055, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016412751, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016413452, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016415246, "dur": 2312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016417558, "dur": 2212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016419770, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016420656, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016421661, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016422561, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016423570, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016424556, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016425306, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016426044, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016426986, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016427650, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016428234, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016428913, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016429541, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016430150, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016431040, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016431748, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016432392, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016432973, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016433520, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016434066, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016434631, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016435198, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016435803, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016436639, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016437291, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016437901, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016438554, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016439123, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016439409, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016440001, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016440164, "dur": 796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724016440960, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016441490, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016441924, "dur": 978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724016442903, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016443335, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016443495, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016443935, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016444241, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724016445278, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016445799, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724016446086, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016446347, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016446420, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016446476, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724016447255, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016447424, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016447535, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016447654, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724016448248, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016448665, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016448909, "dur": 1034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724016449944, "dur": 583, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016450530, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll_D2CC97DA1342C9A4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748724016450581, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016450634, "dur": 1602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748724016452236, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016452690, "dur": 1267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724016453957, "dur": 558969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017012926, "dur": 9959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017022885, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724017025248, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017025434, "dur": 3341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724017028776, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017028835, "dur": 3763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724017032636, "dur": 4472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724017037151, "dur": 1690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724017038856, "dur": 2414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724017041270, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017041521, "dur": 2854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724017044417, "dur": 3611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748724017048028, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017048101, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017048443, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017048832, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017048972, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017049069, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017049307, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017049656, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017049856, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017050038, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017050150, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017050269, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017050475, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017050590, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017050870, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017051028, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017051147, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017051599, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748724017051672, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017051763, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017051821, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017051945, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017052056, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748724017052188, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017052384, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017052436, "dur": 643020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748724017695458, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748724017695523, "dur": 917, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748724016179255, "dur": 222247, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016401505, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016402051, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016402110, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016402300, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016402541, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016402700, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016402947, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748724016403235, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016403296, "dur": 1113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724016404409, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016404729, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748724016404893, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748724016404990, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016405083, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016405378, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016405454, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016405565, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016405673, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016405760, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016405821, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016405932, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016406039, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016406852, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016407472, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016408151, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016408814, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016409561, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016410272, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016411065, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016411778, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016412459, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016413123, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016414327, "dur": 2192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016416519, "dur": 2313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016418833, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016420277, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016421269, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016422138, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016423135, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016424090, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016424998, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016425760, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016426458, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016427382, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016427971, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016428671, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016429300, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016429897, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016430663, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016431489, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016432065, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016432707, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016433260, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016433805, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016434352, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016434910, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016435501, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016436100, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016436860, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016437498, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016438116, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016438743, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016439294, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016439394, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016440125, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016440317, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724016440920, "dur": 652, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016441640, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016442063, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724016442845, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016443055, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_F0EDAB69F288A2F3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016443107, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016443188, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016443548, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016443862, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016444092, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724016444861, "dur": 1441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016446305, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016446421, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724016447109, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016447269, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724016448230, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016448522, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724016449402, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016449477, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016449791, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016449974, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724016450478, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016450905, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016450961, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016451075, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016451353, "dur": 1069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748724016452422, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016452762, "dur": 1172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724016453955, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748724016454116, "dur": 568743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017022860, "dur": 2206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724017025066, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017025157, "dur": 2896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724017028055, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017028123, "dur": 2109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724017030246, "dur": 2291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724017032591, "dur": 2646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724017035237, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017035310, "dur": 3294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724017038604, "dur": 894, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017039503, "dur": 3678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724017043181, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017043239, "dur": 2701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724017045974, "dur": 3647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748724017049622, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017049695, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748724017049746, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017049882, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017050024, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017050191, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017050325, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017050422, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017050535, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748724017050592, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017050806, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017050957, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017051053, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017051402, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017051540, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017051690, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017052064, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748724017052181, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017052275, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748724017052478, "dur": 644020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016179261, "dur": 222247, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016401511, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016401960, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016402141, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016402314, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016402687, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016402759, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016402833, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748724016403242, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016404626, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016404878, "dur": 1646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016406559, "dur": 20845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016427405, "dur": 8811, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016436234, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016436349, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016436418, "dur": 1868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016438321, "dur": 1298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016439620, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016439834, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016439912, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016440661, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016441057, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016441220, "dur": 1247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016442468, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016442888, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016443126, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016443178, "dur": 1773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016444951, "dur": 681, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016445635, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016446078, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016447006, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016447207, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016447502, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016448477, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016448746, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016449332, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016449713, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016449859, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016450486, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016450757, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_AD2FFB7646FD92B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016451004, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016451129, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016451272, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016451902, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016452155, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748724016452809, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016452962, "dur": 968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724016453932, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748724016454050, "dur": 568811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724017022861, "dur": 2945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724017025806, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724017025925, "dur": 4735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724017030661, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724017030830, "dur": 2301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724017033161, "dur": 1784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724017034983, "dur": 3319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724017038303, "dur": 1205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748724017039511, "dur": 1760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724017041305, "dur": 2906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724017044235, "dur": 2896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724017047142, "dur": 5656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748724017052818, "dur": 643683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016179270, "dur": 222252, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016401524, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016401977, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016402163, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016402559, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016402659, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016402715, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016402775, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016402954, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724016403266, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724016403452, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016403543, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724016403891, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748724016404096, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724016404397, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724016404639, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748724016404797, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748724016404977, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016405080, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016405275, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748724016405428, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016405552, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016405664, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016405742, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016405816, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016405921, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016405980, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016406066, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016406908, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016407518, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016408208, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016408867, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016409532, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016410242, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016411045, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016411747, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016412427, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016413101, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016414245, "dur": 2159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016416404, "dur": 2431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016418836, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016420290, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016421302, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016422145, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016423119, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016424080, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016424990, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016425748, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016426443, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016427377, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016427961, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016428658, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016429280, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016429881, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016430621, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016431472, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016432056, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016432693, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016433251, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016433801, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016434361, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016434915, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016435528, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016436112, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016436855, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016437503, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016438121, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016438750, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016439106, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016439272, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016439395, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016439954, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016440143, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748724016440971, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016441635, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016441980, "dur": 959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748724016442939, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016443170, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016443615, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016443984, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016444224, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748724016444512, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748724016445364, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016445418, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1748724016445956, "dur": 76, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016567128, "dur": 454598, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724016446452, "dur": 575289, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1748724017022853, "dur": 4794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724017027648, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724017027860, "dur": 2613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724017030516, "dur": 2779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724017033317, "dur": 5166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724017038497, "dur": 5720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724017044265, "dur": 2436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724017046702, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724017046761, "dur": 5574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748724017052366, "dur": 643079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748724017695515, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748724017699529, "dur": 2446, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 74813, "tid": 1533069, "ts": 1748724017720843, "dur": 2128, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 74813, "tid": 1533069, "ts": 1748724017723045, "dur": 1800, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 74813, "tid": 1533069, "ts": 1748724017716714, "dur": 8654, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}