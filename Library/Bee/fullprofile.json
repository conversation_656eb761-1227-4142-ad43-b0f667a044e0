{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 74813, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 74813, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 74813, "tid": 153858, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 74813, "tid": 153858, "ts": 1748707620247737, "dur": 864, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 74813, "tid": 153858, "ts": 1748707620253623, "dur": 767, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 74813, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707616982970, "dur": 15437, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707616998408, "dur": 3239181, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707616998466, "dur": 114, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707616998598, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707616998603, "dur": 158558, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617157170, "dur": 13, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617157184, "dur": 1223, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158411, "dur": 1, "ph": "X", "name": "ProcessMessages 1950", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158413, "dur": 41, "ph": "X", "name": "ReadAsync 1950", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158456, "dur": 1, "ph": "X", "name": "ProcessMessages 1450", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158457, "dur": 20, "ph": "X", "name": "ReadAsync 1450", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158480, "dur": 33, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158519, "dur": 35, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158556, "dur": 52, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158616, "dur": 1, "ph": "X", "name": "ProcessMessages 1651", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158617, "dur": 25, "ph": "X", "name": "ReadAsync 1651", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158645, "dur": 54, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158704, "dur": 1, "ph": "X", "name": "ProcessMessages 1285", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158706, "dur": 139, "ph": "X", "name": "ReadAsync 1285", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158847, "dur": 2, "ph": "X", "name": "ProcessMessages 4082", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158850, "dur": 48, "ph": "X", "name": "ReadAsync 4082", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158899, "dur": 68, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158969, "dur": 1, "ph": "X", "name": "ProcessMessages 2120", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617158971, "dur": 43, "ph": "X", "name": "ReadAsync 2120", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159015, "dur": 1, "ph": "X", "name": "ProcessMessages 1442", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159017, "dur": 40, "ph": "X", "name": "ReadAsync 1442", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159058, "dur": 6, "ph": "X", "name": "ProcessMessages 1259", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159066, "dur": 44, "ph": "X", "name": "ReadAsync 1259", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159111, "dur": 1, "ph": "X", "name": "ProcessMessages 1492", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159112, "dur": 40, "ph": "X", "name": "ReadAsync 1492", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159157, "dur": 42, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159201, "dur": 49, "ph": "X", "name": "ReadAsync 1480", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159251, "dur": 1, "ph": "X", "name": "ProcessMessages 1346", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159259, "dur": 40, "ph": "X", "name": "ReadAsync 1346", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159300, "dur": 1, "ph": "X", "name": "ProcessMessages 1689", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159301, "dur": 44, "ph": "X", "name": "ReadAsync 1689", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159346, "dur": 1, "ph": "X", "name": "ProcessMessages 1345", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159348, "dur": 45, "ph": "X", "name": "ReadAsync 1345", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159394, "dur": 1, "ph": "X", "name": "ProcessMessages 1414", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159395, "dur": 29, "ph": "X", "name": "ReadAsync 1414", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159426, "dur": 44, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159471, "dur": 1, "ph": "X", "name": "ProcessMessages 1357", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159472, "dur": 44, "ph": "X", "name": "ReadAsync 1357", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159523, "dur": 74, "ph": "X", "name": "ReadAsync 1434", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159599, "dur": 1, "ph": "X", "name": "ProcessMessages 1649", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159601, "dur": 42, "ph": "X", "name": "ReadAsync 1649", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159670, "dur": 1, "ph": "X", "name": "ProcessMessages 1367", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159672, "dur": 29, "ph": "X", "name": "ReadAsync 1367", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159703, "dur": 1, "ph": "X", "name": "ProcessMessages 1680", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159705, "dur": 61, "ph": "X", "name": "ReadAsync 1680", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159767, "dur": 1, "ph": "X", "name": "ProcessMessages 1558", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159768, "dur": 35, "ph": "X", "name": "ReadAsync 1558", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159805, "dur": 113, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159919, "dur": 1, "ph": "X", "name": "ProcessMessages 2294", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617159921, "dur": 77, "ph": "X", "name": "ReadAsync 2294", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160000, "dur": 1, "ph": "X", "name": "ProcessMessages 1145", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160002, "dur": 72, "ph": "X", "name": "ReadAsync 1145", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160075, "dur": 1, "ph": "X", "name": "ProcessMessages 1356", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160076, "dur": 56, "ph": "X", "name": "ReadAsync 1356", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160133, "dur": 1, "ph": "X", "name": "ProcessMessages 1442", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160134, "dur": 35, "ph": "X", "name": "ReadAsync 1442", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160172, "dur": 105, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160298, "dur": 6, "ph": "X", "name": "ProcessMessages 2140", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160305, "dur": 53, "ph": "X", "name": "ReadAsync 2140", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160362, "dur": 14, "ph": "X", "name": "ProcessMessages 2065", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160378, "dur": 22, "ph": "X", "name": "ReadAsync 2065", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160402, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160403, "dur": 48, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160453, "dur": 44, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160510, "dur": 2, "ph": "X", "name": "ProcessMessages 1367", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160512, "dur": 51, "ph": "X", "name": "ReadAsync 1367", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160565, "dur": 1, "ph": "X", "name": "ProcessMessages 1494", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160566, "dur": 34, "ph": "X", "name": "ReadAsync 1494", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160602, "dur": 46, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160649, "dur": 1, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160650, "dur": 38, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160690, "dur": 41, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160732, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160734, "dur": 32, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160768, "dur": 32, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160803, "dur": 45, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160850, "dur": 36, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160889, "dur": 42, "ph": "X", "name": "ReadAsync 1186", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160934, "dur": 43, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617160979, "dur": 39, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161020, "dur": 40, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161063, "dur": 37, "ph": "X", "name": "ReadAsync 1367", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161102, "dur": 42, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161146, "dur": 46, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161194, "dur": 51, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161247, "dur": 1, "ph": "X", "name": "ProcessMessages 1689", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161248, "dur": 22, "ph": "X", "name": "ReadAsync 1689", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161272, "dur": 62, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161335, "dur": 1, "ph": "X", "name": "ProcessMessages 1541", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161337, "dur": 31, "ph": "X", "name": "ReadAsync 1541", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161370, "dur": 205, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161576, "dur": 1, "ph": "X", "name": "ProcessMessages 1573", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161577, "dur": 31, "ph": "X", "name": "ReadAsync 1573", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161610, "dur": 2, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161613, "dur": 27, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161650, "dur": 44, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161695, "dur": 1, "ph": "X", "name": "ProcessMessages 1556", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161697, "dur": 76, "ph": "X", "name": "ReadAsync 1556", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161774, "dur": 1, "ph": "X", "name": "ProcessMessages 1828", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161775, "dur": 35, "ph": "X", "name": "ReadAsync 1828", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161811, "dur": 6, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161818, "dur": 36, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161856, "dur": 38, "ph": "X", "name": "ReadAsync 1424", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161896, "dur": 26, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161925, "dur": 22, "ph": "X", "name": "ReadAsync 1051", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161949, "dur": 38, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617161989, "dur": 25, "ph": "X", "name": "ReadAsync 1267", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162016, "dur": 40, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162059, "dur": 43, "ph": "X", "name": "ReadAsync 1275", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162104, "dur": 61, "ph": "X", "name": "ReadAsync 1305", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162166, "dur": 1, "ph": "X", "name": "ProcessMessages 1808", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162171, "dur": 21, "ph": "X", "name": "ReadAsync 1808", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162194, "dur": 63, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162258, "dur": 1, "ph": "X", "name": "ProcessMessages 1871", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162259, "dur": 48, "ph": "X", "name": "ReadAsync 1871", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162309, "dur": 37, "ph": "X", "name": "ReadAsync 1230", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162348, "dur": 47, "ph": "X", "name": "ReadAsync 1207", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162397, "dur": 43, "ph": "X", "name": "ReadAsync 1314", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162443, "dur": 35, "ph": "X", "name": "ReadAsync 1344", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162480, "dur": 52, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162533, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162535, "dur": 42, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162579, "dur": 33, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162615, "dur": 38, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162655, "dur": 44, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162701, "dur": 36, "ph": "X", "name": "ReadAsync 1248", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162739, "dur": 45, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162787, "dur": 50, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162839, "dur": 36, "ph": "X", "name": "ReadAsync 1178", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162877, "dur": 37, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162916, "dur": 42, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617162963, "dur": 42, "ph": "X", "name": "ReadAsync 1323", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163007, "dur": 63, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163073, "dur": 55, "ph": "X", "name": "ReadAsync 2028", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163130, "dur": 37, "ph": "X", "name": "ReadAsync 1744", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163169, "dur": 37, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163212, "dur": 25, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163239, "dur": 78, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163318, "dur": 1, "ph": "X", "name": "ProcessMessages 1780", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163320, "dur": 43, "ph": "X", "name": "ReadAsync 1780", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163364, "dur": 36, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163408, "dur": 42, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163452, "dur": 40, "ph": "X", "name": "ReadAsync 1257", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163494, "dur": 49, "ph": "X", "name": "ReadAsync 1091", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163545, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163550, "dur": 46, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163598, "dur": 43, "ph": "X", "name": "ReadAsync 1322", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163642, "dur": 1, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163644, "dur": 44, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163722, "dur": 50, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163773, "dur": 1, "ph": "X", "name": "ProcessMessages 2399", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163775, "dur": 44, "ph": "X", "name": "ReadAsync 2399", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163821, "dur": 20, "ph": "X", "name": "ReadAsync 1346", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163860, "dur": 21, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163883, "dur": 52, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163936, "dur": 1, "ph": "X", "name": "ProcessMessages 1822", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163938, "dur": 46, "ph": "X", "name": "ReadAsync 1822", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617163986, "dur": 49, "ph": "X", "name": "ReadAsync 1391", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164037, "dur": 37, "ph": "X", "name": "ReadAsync 1495", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164076, "dur": 1, "ph": "X", "name": "ProcessMessages 1258", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164077, "dur": 44, "ph": "X", "name": "ReadAsync 1258", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164123, "dur": 87, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164211, "dur": 1, "ph": "X", "name": "ProcessMessages 1683", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164212, "dur": 44, "ph": "X", "name": "ReadAsync 1683", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164258, "dur": 1, "ph": "X", "name": "ProcessMessages 1673", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164259, "dur": 39, "ph": "X", "name": "ReadAsync 1673", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164300, "dur": 41, "ph": "X", "name": "ReadAsync 1335", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164343, "dur": 27, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164373, "dur": 40, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164414, "dur": 42, "ph": "X", "name": "ReadAsync 1115", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164458, "dur": 37, "ph": "X", "name": "ReadAsync 1193", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164497, "dur": 47, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164548, "dur": 1, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164550, "dur": 47, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164643, "dur": 1, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164645, "dur": 19, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164665, "dur": 1, "ph": "X", "name": "ProcessMessages 1495", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164666, "dur": 36, "ph": "X", "name": "ReadAsync 1495", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164709, "dur": 36, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164747, "dur": 46, "ph": "X", "name": "ReadAsync 1115", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164795, "dur": 42, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164839, "dur": 91, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164933, "dur": 22, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617164957, "dur": 80, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165040, "dur": 38, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165080, "dur": 67, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165149, "dur": 33, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165184, "dur": 85, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165271, "dur": 108, "ph": "X", "name": "ReadAsync 1071", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165381, "dur": 1, "ph": "X", "name": "ProcessMessages 1329", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165382, "dur": 40, "ph": "X", "name": "ReadAsync 1329", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165424, "dur": 45, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165473, "dur": 45, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165521, "dur": 73, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165598, "dur": 52, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165652, "dur": 36, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165691, "dur": 85, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165778, "dur": 110, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165889, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165891, "dur": 61, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165961, "dur": 21, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617165984, "dur": 23, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166008, "dur": 40, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166050, "dur": 29, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166081, "dur": 42, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166125, "dur": 56, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166183, "dur": 1, "ph": "X", "name": "ProcessMessages 1473", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166184, "dur": 87, "ph": "X", "name": "ReadAsync 1473", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166273, "dur": 143, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166417, "dur": 1, "ph": "X", "name": "ProcessMessages 2621", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166419, "dur": 52, "ph": "X", "name": "ReadAsync 2621", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166473, "dur": 59, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166534, "dur": 106, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166641, "dur": 1, "ph": "X", "name": "ProcessMessages 2700", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166644, "dur": 72, "ph": "X", "name": "ReadAsync 2700", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166718, "dur": 37, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166757, "dur": 67, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166827, "dur": 1, "ph": "X", "name": "ProcessMessages 1261", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166829, "dur": 88, "ph": "X", "name": "ReadAsync 1261", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166922, "dur": 4, "ph": "X", "name": "ProcessMessages 1463", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617166929, "dur": 148, "ph": "X", "name": "ReadAsync 1463", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167078, "dur": 1, "ph": "X", "name": "ProcessMessages 1596", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167081, "dur": 75, "ph": "X", "name": "ReadAsync 1596", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167158, "dur": 124, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167283, "dur": 1, "ph": "X", "name": "ProcessMessages 1294", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167291, "dur": 81, "ph": "X", "name": "ReadAsync 1294", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167374, "dur": 34, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167409, "dur": 2, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167412, "dur": 91, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167504, "dur": 1, "ph": "X", "name": "ProcessMessages 1400", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167505, "dur": 35, "ph": "X", "name": "ReadAsync 1400", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167542, "dur": 38, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167583, "dur": 51, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167639, "dur": 32, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167673, "dur": 34, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167709, "dur": 70, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167781, "dur": 93, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167876, "dur": 1, "ph": "X", "name": "ProcessMessages 1502", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167877, "dur": 34, "ph": "X", "name": "ReadAsync 1502", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167913, "dur": 36, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617167951, "dur": 79, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168033, "dur": 76, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168111, "dur": 72, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168186, "dur": 69, "ph": "X", "name": "ReadAsync 1261", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168257, "dur": 48, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168307, "dur": 1, "ph": "X", "name": "ProcessMessages 1181", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168308, "dur": 64, "ph": "X", "name": "ReadAsync 1181", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168374, "dur": 39, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168420, "dur": 45, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168467, "dur": 47, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168517, "dur": 96, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168614, "dur": 1, "ph": "X", "name": "ProcessMessages 1234", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168616, "dur": 31, "ph": "X", "name": "ReadAsync 1234", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168649, "dur": 42, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168692, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168693, "dur": 36, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168731, "dur": 24, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168756, "dur": 17, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168775, "dur": 44, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168822, "dur": 48, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168873, "dur": 25, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168903, "dur": 44, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168949, "dur": 25, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617168975, "dur": 19, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169002, "dur": 39, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169047, "dur": 72, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169122, "dur": 36, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169161, "dur": 89, "ph": "X", "name": "ReadAsync 1271", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169251, "dur": 1, "ph": "X", "name": "ProcessMessages 1639", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169253, "dur": 34, "ph": "X", "name": "ReadAsync 1639", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169289, "dur": 69, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169361, "dur": 20, "ph": "X", "name": "ReadAsync 1433", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169383, "dur": 25, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169410, "dur": 122, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169534, "dur": 46, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169583, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169642, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169751, "dur": 14, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169766, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617169924, "dur": 117, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617170043, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617170162, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617170276, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617170391, "dur": 155, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617170548, "dur": 175, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617170731, "dur": 111, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617170844, "dur": 110, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617170956, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617171050, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617171230, "dur": 122, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617171354, "dur": 160, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617171516, "dur": 81, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617171599, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617171730, "dur": 132, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617171864, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617171899, "dur": 62, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617171963, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617171994, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617172156, "dur": 118, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617172275, "dur": 118, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617172396, "dur": 104, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617172502, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617172536, "dur": 160, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617172698, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617172720, "dur": 266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617172989, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173106, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173150, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173234, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173354, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173382, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173470, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173517, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173635, "dur": 35, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173672, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173784, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173923, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617173960, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174042, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174135, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174191, "dur": 108, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174302, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174331, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174376, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174423, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174492, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174519, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174552, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174642, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174683, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174734, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174779, "dur": 48, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174829, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617174910, "dur": 126, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175038, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175075, "dur": 59, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175136, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175175, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175202, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175238, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175264, "dur": 75, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175340, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175469, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175517, "dur": 75, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175594, "dur": 83, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175683, "dur": 64, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175748, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175750, "dur": 51, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175803, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175852, "dur": 45, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175899, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617175961, "dur": 76, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176039, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176072, "dur": 82, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176157, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176217, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176252, "dur": 195, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176450, "dur": 94, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176546, "dur": 110, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176662, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176681, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176758, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176791, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176818, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176890, "dur": 96, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176987, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617176988, "dur": 75, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177065, "dur": 71, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177138, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177186, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177235, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177264, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177272, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177336, "dur": 107, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177458, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177507, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177541, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177567, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177630, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177652, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177843, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177845, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177909, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617177910, "dur": 99, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178012, "dur": 117, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178131, "dur": 68, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178201, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178280, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178366, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178394, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178473, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178548, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178591, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178615, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178657, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178705, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178736, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178814, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178866, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178956, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617178980, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617179109, "dur": 72, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617179183, "dur": 69, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617179254, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617179292, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617179323, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617179412, "dur": 589, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617180003, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617180036, "dur": 401, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617180438, "dur": 1736, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617182175, "dur": 9870, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617192049, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617192051, "dur": 231, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617192285, "dur": 1696, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617193985, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617194194, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617194199, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617194379, "dur": 2338, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617196719, "dur": 23016, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617219739, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617219743, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617219830, "dur": 1217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617221049, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617221050, "dur": 642, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617221696, "dur": 357, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617222057, "dur": 220, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617222285, "dur": 388, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617222676, "dur": 309, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617222989, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617223045, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617223049, "dur": 106, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617223157, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617223278, "dur": 207, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617223487, "dur": 313, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617223802, "dur": 204, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617224008, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617224037, "dur": 296, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617224335, "dur": 293, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617224631, "dur": 243, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617224968, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617225061, "dur": 108, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617225171, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617225267, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617225270, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617225434, "dur": 216, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617225652, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617225735, "dur": 205, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617225941, "dur": 259, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617226203, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617226206, "dur": 225, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617226434, "dur": 59, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617226494, "dur": 223, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617226719, "dur": 290, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617227011, "dur": 126, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617227140, "dur": 187, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617227329, "dur": 472, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617227803, "dur": 265, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617228070, "dur": 341, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617228413, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617228665, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617228669, "dur": 314, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617228985, "dur": 158, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617229146, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617229148, "dur": 331, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617229491, "dur": 286, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617229780, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617229783, "dur": 415, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617230201, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617230204, "dur": 305, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617230513, "dur": 327, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617230842, "dur": 436, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617231294, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617231298, "dur": 193, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617231495, "dur": 160, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617231657, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617231734, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617231794, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617231798, "dur": 61, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617231861, "dur": 181, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617232045, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617232048, "dur": 91, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617232141, "dur": 218, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617232361, "dur": 152, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617232515, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617232518, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617232659, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617232662, "dur": 184, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617232849, "dur": 234, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617233086, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617233089, "dur": 625, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617233716, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617233719, "dur": 438, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617234159, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617234161, "dur": 270, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617234433, "dur": 246, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617234686, "dur": 227, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617234916, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617234919, "dur": 175, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617235097, "dur": 338, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617235438, "dur": 150, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617235590, "dur": 194, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617235788, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617235790, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617235862, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617236004, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617236019, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617236089, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617236282, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617236285, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617236334, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617236569, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617236667, "dur": 273, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617236942, "dur": 501, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617237446, "dur": 270, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617237719, "dur": 260, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617237985, "dur": 413, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617238400, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617238560, "dur": 114, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617238676, "dur": 378, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617239055, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617239079, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617239251, "dur": 570, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617239824, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617239828, "dur": 262, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617240094, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617240145, "dur": 272, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617240419, "dur": 453, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617240874, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617240876, "dur": 107146, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617348039, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617348042, "dur": 41, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617348086, "dur": 73, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617348196, "dur": 46, "ph": "X", "name": "ReadAsync 7427", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617348251, "dur": 49, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617348302, "dur": 71, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617348376, "dur": 66, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617348443, "dur": 40, "ph": "X", "name": "ProcessMessages 2850", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617348497, "dur": 3984, "ph": "X", "name": "ReadAsync 2850", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617352483, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617352485, "dur": 495, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617352985, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617352989, "dur": 457, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617353449, "dur": 1526, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617354980, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617354983, "dur": 689, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617355675, "dur": 503, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617356181, "dur": 398, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617356581, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617356946, "dur": 844, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617357792, "dur": 714, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617358509, "dur": 2390, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617360901, "dur": 163, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617361067, "dur": 1086, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617362156, "dur": 603, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617362761, "dur": 519, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617363282, "dur": 489, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617363773, "dur": 1106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617364882, "dur": 694, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617365578, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617365721, "dur": 754, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617366477, "dur": 1599, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617368078, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617368184, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617368231, "dur": 867, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617369101, "dur": 1594, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617370697, "dur": 789, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617371488, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617371547, "dur": 645, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617372194, "dur": 316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617372511, "dur": 1800, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617374314, "dur": 373, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617374689, "dur": 622, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617375317, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617375321, "dur": 1382, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617376705, "dur": 1351, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378058, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378151, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378277, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378300, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378418, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378510, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378544, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378684, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378757, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378835, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378885, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378958, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378959, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617378989, "dur": 92, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379083, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379243, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379268, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379299, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379369, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379405, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379500, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379521, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379631, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379686, "dur": 136, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379825, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379860, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379926, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617379927, "dur": 92, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380022, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380101, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380153, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380261, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380289, "dur": 38, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380328, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380350, "dur": 66, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380418, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380466, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380513, "dur": 145, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380660, "dur": 93, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380755, "dur": 100, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380857, "dur": 62, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380922, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617380988, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617381010, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617381111, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617381138, "dur": 42, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617381182, "dur": 99, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617381286, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617381290, "dur": 503, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617381794, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617381800, "dur": 326, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707617382128, "dur": 2059961, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707619442107, "dur": 78, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707619442186, "dur": 1768, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707619443957, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707619443960, "dur": 1605, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707619445572, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707619445575, "dur": 286585, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707619732184, "dur": 79, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707619732265, "dur": 4709, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707619736993, "dur": 6, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707619737001, "dur": 441203, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620178214, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620178217, "dur": 83, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620178302, "dur": 146, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620178458, "dur": 132, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620178598, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620178601, "dur": 223, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620178826, "dur": 71, "ph": "X", "name": "ProcessMessages 7969", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620178901, "dur": 5945, "ph": "X", "name": "ReadAsync 7969", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620184852, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620184856, "dur": 574, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620185434, "dur": 17, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620185452, "dur": 365, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620185820, "dur": 12, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620185833, "dur": 39891, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620225731, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620225733, "dur": 72, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620225807, "dur": 49, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620225859, "dur": 94, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620225962, "dur": 57, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620226021, "dur": 20, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620226041, "dur": 4218, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620230265, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620230269, "dur": 1073, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620231347, "dur": 52, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620231401, "dur": 431, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620231898, "dur": 11, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 74813, "tid": 21474836480, "ts": 1748707620231910, "dur": 5566, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 74813, "tid": 153858, "ts": 1748707620254396, "dur": 1514, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 74813, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 74813, "tid": 17179869184, "ts": 1748707616982777, "dur": 43, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 74813, "tid": 17179869184, "ts": 1748707616982820, "dur": 15586, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 74813, "tid": 17179869184, "ts": 1748707616998408, "dur": 133, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 74813, "tid": 153858, "ts": 1748707620255913, "dur": 21, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 74813, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 74813, "tid": 1, "ts": 1748707615911392, "dur": 5691, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 74813, "tid": 1, "ts": 1748707615917087, "dur": 47609, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 74813, "tid": 1, "ts": 1748707615964705, "dur": 41960, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 74813, "tid": 153858, "ts": 1748707620255936, "dur": 16, "ph": "X", "name": "", "args": {}}, {"pid": 74813, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615910038, "dur": 11544, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615921585, "dur": 98379, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615922352, "dur": 2341, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615924703, "dur": 1152, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615925857, "dur": 16139, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942016, "dur": 365, "ph": "X", "name": "ProcessMessages 106", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942385, "dur": 66, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942453, "dur": 3, "ph": "X", "name": "ProcessMessages 8168", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942457, "dur": 91, "ph": "X", "name": "ReadAsync 8168", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942550, "dur": 1, "ph": "X", "name": "ProcessMessages 2625", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942555, "dur": 67, "ph": "X", "name": "ReadAsync 2625", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942622, "dur": 1, "ph": "X", "name": "ProcessMessages 1782", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942624, "dur": 110, "ph": "X", "name": "ReadAsync 1782", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942736, "dur": 2, "ph": "X", "name": "ProcessMessages 3237", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942739, "dur": 102, "ph": "X", "name": "ReadAsync 3237", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942842, "dur": 1, "ph": "X", "name": "ProcessMessages 3094", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942874, "dur": 41, "ph": "X", "name": "ReadAsync 3094", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942916, "dur": 1, "ph": "X", "name": "ProcessMessages 2001", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942918, "dur": 59, "ph": "X", "name": "ReadAsync 2001", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942984, "dur": 1, "ph": "X", "name": "ProcessMessages 1813", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615942985, "dur": 49, "ph": "X", "name": "ReadAsync 1813", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943079, "dur": 1, "ph": "X", "name": "ProcessMessages 1879", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943081, "dur": 66, "ph": "X", "name": "ReadAsync 1879", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943150, "dur": 1, "ph": "X", "name": "ProcessMessages 2014", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943162, "dur": 47, "ph": "X", "name": "ReadAsync 2014", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943212, "dur": 1, "ph": "X", "name": "ProcessMessages 2539", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943213, "dur": 48, "ph": "X", "name": "ReadAsync 2539", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943264, "dur": 49, "ph": "X", "name": "ReadAsync 1495", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943315, "dur": 1, "ph": "X", "name": "ProcessMessages 1589", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943320, "dur": 28, "ph": "X", "name": "ReadAsync 1589", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943350, "dur": 1, "ph": "X", "name": "ProcessMessages 1020", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943384, "dur": 17, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943402, "dur": 1, "ph": "X", "name": "ProcessMessages 1420", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615943404, "dur": 2260, "ph": "X", "name": "ReadAsync 1420", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615945665, "dur": 1, "ph": "X", "name": "ProcessMessages 1434", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615945667, "dur": 41, "ph": "X", "name": "ReadAsync 1434", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615945735, "dur": 1, "ph": "X", "name": "ProcessMessages 1877", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615945739, "dur": 33, "ph": "X", "name": "ReadAsync 1877", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615945817, "dur": 98, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615945917, "dur": 131, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615946050, "dur": 30, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615946082, "dur": 210, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615946344, "dur": 546, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615946891, "dur": 7, "ph": "X", "name": "ProcessMessages 1625", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615946899, "dur": 74, "ph": "X", "name": "ReadAsync 1625", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615947607, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615947836, "dur": 51, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615947905, "dur": 4, "ph": "X", "name": "ProcessMessages 8166", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615947910, "dur": 30, "ph": "X", "name": "ReadAsync 8166", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615947944, "dur": 29, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615947973, "dur": 23, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615947998, "dur": 1, "ph": "X", "name": "ProcessMessages 1227", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615947999, "dur": 95, "ph": "X", "name": "ReadAsync 1227", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615948096, "dur": 1, "ph": "X", "name": "ProcessMessages 2505", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615948098, "dur": 91, "ph": "X", "name": "ReadAsync 2505", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615948190, "dur": 1, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615948192, "dur": 567, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615948760, "dur": 4, "ph": "X", "name": "ProcessMessages 5980", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615948784, "dur": 77, "ph": "X", "name": "ReadAsync 5980", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615948862, "dur": 1, "ph": "X", "name": "ProcessMessages 1750", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615948864, "dur": 1849, "ph": "X", "name": "ReadAsync 1750", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615950726, "dur": 3, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615950730, "dur": 90, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615950822, "dur": 4, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615950826, "dur": 18, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615950852, "dur": 5, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615950861, "dur": 29, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615951206, "dur": 17, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615951234, "dur": 2, "ph": "X", "name": "ProcessMessages 3980", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615951258, "dur": 53, "ph": "X", "name": "ReadAsync 3980", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615951312, "dur": 1, "ph": "X", "name": "ProcessMessages 1430", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615951314, "dur": 25, "ph": "X", "name": "ReadAsync 1430", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615951341, "dur": 686, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952032, "dur": 1, "ph": "X", "name": "ProcessMessages 1163", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952043, "dur": 25, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952079, "dur": 3, "ph": "X", "name": "ProcessMessages 7277", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952103, "dur": 188, "ph": "X", "name": "ReadAsync 7277", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952305, "dur": 67, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952376, "dur": 44, "ph": "X", "name": "ReadAsync 1284", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952428, "dur": 33, "ph": "X", "name": "ReadAsync 1616", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952465, "dur": 1, "ph": "X", "name": "ProcessMessages 1027", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952467, "dur": 25, "ph": "X", "name": "ReadAsync 1027", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952498, "dur": 25, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952525, "dur": 51, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952583, "dur": 49, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952633, "dur": 1, "ph": "X", "name": "ProcessMessages 1644", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952644, "dur": 69, "ph": "X", "name": "ReadAsync 1644", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952715, "dur": 25, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952746, "dur": 17, "ph": "X", "name": "ReadAsync 1605", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952767, "dur": 28, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952800, "dur": 3, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952804, "dur": 24, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952863, "dur": 59, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952923, "dur": 1, "ph": "X", "name": "ProcessMessages 2416", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952925, "dur": 26, "ph": "X", "name": "ReadAsync 2416", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952956, "dur": 25, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615952983, "dur": 48, "ph": "X", "name": "ReadAsync 985", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953032, "dur": 1, "ph": "X", "name": "ProcessMessages 1492", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953034, "dur": 102, "ph": "X", "name": "ReadAsync 1492", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953138, "dur": 24, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953162, "dur": 1, "ph": "X", "name": "ProcessMessages 2330", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953164, "dur": 53, "ph": "X", "name": "ReadAsync 2330", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953220, "dur": 114, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953335, "dur": 1, "ph": "X", "name": "ProcessMessages 2537", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953337, "dur": 42, "ph": "X", "name": "ReadAsync 2537", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953384, "dur": 9, "ph": "X", "name": "ProcessMessages 2240", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953400, "dur": 71, "ph": "X", "name": "ReadAsync 2240", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953476, "dur": 6, "ph": "X", "name": "ProcessMessages 1699", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953486, "dur": 41, "ph": "X", "name": "ReadAsync 1699", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953529, "dur": 19, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953571, "dur": 20, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953593, "dur": 29, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953622, "dur": 6, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953630, "dur": 36, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953685, "dur": 30, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953721, "dur": 1, "ph": "X", "name": "ProcessMessages 1183", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953723, "dur": 26, "ph": "X", "name": "ReadAsync 1183", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953752, "dur": 30, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953783, "dur": 35, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953821, "dur": 41, "ph": "X", "name": "ReadAsync 1116", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953868, "dur": 1, "ph": "X", "name": "ProcessMessages 1270", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953873, "dur": 21, "ph": "X", "name": "ReadAsync 1270", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615953979, "dur": 24, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954004, "dur": 1, "ph": "X", "name": "ProcessMessages 2319", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954005, "dur": 54, "ph": "X", "name": "ReadAsync 2319", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954069, "dur": 1, "ph": "X", "name": "ProcessMessages 1709", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954070, "dur": 40, "ph": "X", "name": "ReadAsync 1709", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954114, "dur": 1, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954115, "dur": 69, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954187, "dur": 1, "ph": "X", "name": "ProcessMessages 2034", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954192, "dur": 20, "ph": "X", "name": "ReadAsync 2034", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954214, "dur": 58, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954273, "dur": 1, "ph": "X", "name": "ProcessMessages 1467", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954274, "dur": 18, "ph": "X", "name": "ReadAsync 1467", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954345, "dur": 39, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954386, "dur": 1, "ph": "X", "name": "ProcessMessages 2089", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954392, "dur": 36, "ph": "X", "name": "ReadAsync 2089", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954456, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954476, "dur": 1, "ph": "X", "name": "ProcessMessages 1944", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954478, "dur": 16, "ph": "X", "name": "ReadAsync 1944", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954500, "dur": 29, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954533, "dur": 35, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954568, "dur": 6, "ph": "X", "name": "ProcessMessages 1269", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954578, "dur": 22, "ph": "X", "name": "ReadAsync 1269", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954603, "dur": 17, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954622, "dur": 17, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954642, "dur": 18, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954724, "dur": 37, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954763, "dur": 1, "ph": "X", "name": "ProcessMessages 2618", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954764, "dur": 19, "ph": "X", "name": "ReadAsync 2618", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954829, "dur": 98, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954928, "dur": 1, "ph": "X", "name": "ProcessMessages 3028", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954930, "dur": 26, "ph": "X", "name": "ReadAsync 3028", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954958, "dur": 3, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954967, "dur": 27, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954996, "dur": 1, "ph": "X", "name": "ProcessMessages 1005", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615954998, "dur": 50, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955053, "dur": 23, "ph": "X", "name": "ReadAsync 1315", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955090, "dur": 21, "ph": "X", "name": "ReadAsync 1070", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955126, "dur": 38, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955168, "dur": 1, "ph": "X", "name": "ProcessMessages 1807", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955170, "dur": 20, "ph": "X", "name": "ReadAsync 1807", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955252, "dur": 126, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955382, "dur": 14, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955398, "dur": 19, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955424, "dur": 45, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955471, "dur": 179, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955651, "dur": 1, "ph": "X", "name": "ProcessMessages 2215", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955653, "dur": 97, "ph": "X", "name": "ReadAsync 2215", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955751, "dur": 1, "ph": "X", "name": "ProcessMessages 1248", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615955754, "dur": 38, "ph": "X", "name": "ReadAsync 1248", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615958186, "dur": 8, "ph": "X", "name": "ProcessMessages 2242", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615958195, "dur": 75, "ph": "X", "name": "ReadAsync 2242", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615958271, "dur": 1, "ph": "X", "name": "ProcessMessages 1902", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615958273, "dur": 69, "ph": "X", "name": "ReadAsync 1902", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615958373, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615958375, "dur": 1040, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615959417, "dur": 151, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615959571, "dur": 536, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615960109, "dur": 84, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615960195, "dur": 62, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615960260, "dur": 750, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615961012, "dur": 187, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615961203, "dur": 144, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615961349, "dur": 197, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615961549, "dur": 264, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615961817, "dur": 105, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615961926, "dur": 476, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615962413, "dur": 599, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615963014, "dur": 156, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615963173, "dur": 466, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615963641, "dur": 249, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615963892, "dur": 416, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615964311, "dur": 301, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615964614, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615964616, "dur": 642, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615965260, "dur": 22, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615965284, "dur": 786, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615966073, "dur": 15, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615966090, "dur": 787, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615966879, "dur": 20, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615966901, "dur": 472, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615967375, "dur": 64, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615967441, "dur": 183, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615967627, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615967649, "dur": 17, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615967667, "dur": 162, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615967832, "dur": 567, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615968401, "dur": 141, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615968543, "dur": 1, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615968544, "dur": 34, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615968581, "dur": 841, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615969424, "dur": 484, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615969917, "dur": 9, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615969927, "dur": 310, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615970239, "dur": 42, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615970282, "dur": 1, "ph": "X", "name": "ProcessMessages 1296", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615970283, "dur": 63, "ph": "X", "name": "ReadAsync 1296", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615970355, "dur": 884, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615971242, "dur": 127, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615971370, "dur": 210, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615971583, "dur": 57, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615971667, "dur": 22, "ph": "X", "name": "ReadAsync 1511", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615971690, "dur": 596, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615972288, "dur": 141, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615972432, "dur": 458, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615972892, "dur": 124, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615973018, "dur": 740, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615973760, "dur": 227, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615973988, "dur": 1, "ph": "X", "name": "ProcessMessages 2190", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615973990, "dur": 41, "ph": "X", "name": "ReadAsync 2190", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615974033, "dur": 798, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615974834, "dur": 55, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615974891, "dur": 1527, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615976425, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615976426, "dur": 424, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615976853, "dur": 621, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615977476, "dur": 20, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615977498, "dur": 24, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615977525, "dur": 103, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615977630, "dur": 330, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615977963, "dur": 32, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615977997, "dur": 300, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615978299, "dur": 25, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615978325, "dur": 567, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615978894, "dur": 143, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615979040, "dur": 538, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615979580, "dur": 182, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615979764, "dur": 482, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615980256, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615980282, "dur": 124, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615980416, "dur": 505, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615980923, "dur": 142, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615981068, "dur": 502, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615981572, "dur": 23, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615981597, "dur": 597, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615982198, "dur": 21, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615982221, "dur": 707, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615982931, "dur": 173, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615983106, "dur": 519, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615983627, "dur": 30, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615983659, "dur": 1016, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615984677, "dur": 136, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615984815, "dur": 572, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615985390, "dur": 132, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615985529, "dur": 470, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615986000, "dur": 23, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615986025, "dur": 646, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615986674, "dur": 65, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615986746, "dur": 361, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615987109, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615987111, "dur": 333, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615987448, "dur": 436, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615987887, "dur": 381, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615988272, "dur": 139, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615988412, "dur": 1, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615988414, "dur": 85, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615988502, "dur": 6284, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615994790, "dur": 5, "ph": "X", "name": "ProcessMessages 7731", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615994830, "dur": 239, "ph": "X", "name": "ReadAsync 7731", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615995070, "dur": 1, "ph": "X", "name": "ProcessMessages 1353", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615995071, "dur": 105, "ph": "X", "name": "ReadAsync 1353", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615995180, "dur": 187, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615995369, "dur": 1, "ph": "X", "name": "ProcessMessages 1464", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615995372, "dur": 62, "ph": "X", "name": "ReadAsync 1464", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615995508, "dur": 72, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615995581, "dur": 1, "ph": "X", "name": "ProcessMessages 2367", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615995623, "dur": 684, "ph": "X", "name": "ReadAsync 2367", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615996309, "dur": 3, "ph": "X", "name": "ProcessMessages 2231", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615996312, "dur": 28, "ph": "X", "name": "ReadAsync 2231", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615996341, "dur": 7, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615996350, "dur": 766, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615997118, "dur": 1313, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615998432, "dur": 1, "ph": "X", "name": "ProcessMessages 2009", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615998435, "dur": 338, "ph": "X", "name": "ReadAsync 2009", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615998775, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615998776, "dur": 719, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707615999497, "dur": 553, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616000051, "dur": 1754, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616001808, "dur": 74, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616001885, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616001920, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002000, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002081, "dur": 257, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002341, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002342, "dur": 33, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002377, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002405, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002527, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002634, "dur": 71, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002708, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002777, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002878, "dur": 21, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002901, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616002925, "dur": 108, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003035, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003061, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003142, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003220, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003277, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003343, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003424, "dur": 79, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003519, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003549, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003642, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003678, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003742, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003795, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003869, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003900, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616003979, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004023, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004102, "dur": 41, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004145, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004224, "dur": 71, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004297, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004335, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004456, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004510, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004559, "dur": 20, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004581, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004643, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004680, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004773, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004830, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004860, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004863, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004966, "dur": 13, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616004981, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005021, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005157, "dur": 110, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005269, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005363, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005434, "dur": 7, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005442, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005471, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005564, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005594, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005737, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616005777, "dur": 577, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616006357, "dur": 5, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616006364, "dur": 53, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616006419, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616006421, "dur": 186, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616006610, "dur": 286, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616006900, "dur": 309, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616007211, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616007212, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616007244, "dur": 414, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616007660, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616007749, "dur": 326, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616008077, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616008115, "dur": 424, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616008542, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616008686, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616008849, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616008911, "dur": 1011, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616009925, "dur": 398, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616010347, "dur": 15, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616010364, "dur": 65, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616010432, "dur": 110, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616010572, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616010600, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616010729, "dur": 113, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616010844, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616010957, "dur": 414, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011373, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011375, "dur": 25, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011402, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011420, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011439, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011455, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011479, "dur": 29, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011510, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011567, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011686, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011879, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616011905, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616012193, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616012194, "dur": 79, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616012274, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616012400, "dur": 156, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616012559, "dur": 361, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616012930, "dur": 1340, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616014272, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616014350, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616014433, "dur": 164, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 74813, "tid": 12884901888, "ts": 1748707616014598, "dur": 5319, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 74813, "tid": 153858, "ts": 1748707620255953, "dur": 862, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 74813, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 74813, "tid": 8589934592, "ts": 1748707615904822, "dur": 101881, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 74813, "tid": 8589934592, "ts": 1748707616006706, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 74813, "tid": 8589934592, "ts": 1748707616006712, "dur": 1771, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 74813, "tid": 153858, "ts": 1748707620256817, "dur": 13, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 74813, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 74813, "tid": 4294967296, "ts": 1748707615819889, "dur": 201126, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748707615827613, "dur": 71306, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748707616021122, "dur": 932860, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748707616954246, "dur": 3283845, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748707616954488, "dur": 28110, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748707620238226, "dur": 6493, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748707620243007, "dur": 40, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 74813, "tid": 4294967296, "ts": 1748707620244728, "dur": 21, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 74813, "tid": 153858, "ts": 1748707620256838, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748707616998683, "dur": 159738, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707617158424, "dur": 193, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707617158689, "dur": 80, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707617167251, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748707617158775, "dur": 11111, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707617169894, "dur": 3061873, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707620231773, "dur": 55, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707620231934, "dur": 60, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707620232039, "dur": 1385, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748707617158722, "dur": 11178, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617170143, "dur": 150, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1748707617170293, "dur": 1170, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1748707617171463, "dur": 649, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1748707617169901, "dur": 2211, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617172112, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617172225, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617172397, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617172527, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617172632, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617172814, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617172955, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617173086, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617173387, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617173540, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617173653, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617173816, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617173933, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617174051, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617174156, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617174272, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617174438, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617174557, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617174703, "dur": 5254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617179957, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617180041, "dur": 14140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617194181, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617194353, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617194450, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617194530, "dur": 2507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617197037, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617197104, "dur": 22568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617219673, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617219980, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617220183, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617220530, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617220775, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617220886, "dur": 1097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617221983, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617222483, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617223281, "dur": 1181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617224462, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617224551, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617225041, "dur": 1054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617226095, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617226278, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617226837, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617227051, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707617227248, "dur": 944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617228193, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617228323, "dur": 3106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748707617231463, "dur": 189, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617348240, "dur": 584, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617231657, "dur": 117192, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748707617349972, "dur": 2500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617352472, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617352574, "dur": 3650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617356225, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617356320, "dur": 3584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617359906, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617359970, "dur": 3039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617363061, "dur": 2904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617365965, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617366029, "dur": 2536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617368565, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617368637, "dur": 3939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617372576, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617372696, "dur": 4387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617377084, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707617377140, "dur": 5047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748707617382216, "dur": 2849563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617158724, "dur": 11179, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617169963, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617170078, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617170272, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617170392, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_620BC00CDC1CB284.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617170631, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617170743, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617170931, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617171043, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617171241, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617171447, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617171620, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617171733, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617171854, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617171939, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617172169, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617172302, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617172482, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617172550, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617172722, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617172830, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617172994, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617173072, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617173426, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617173512, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617173658, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617173761, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617173999, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617174088, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617174210, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617174329, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617174490, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617174566, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617174685, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617174761, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617174924, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617175025, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617175119, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617175198, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617175307, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617175409, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617175571, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617175686, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617175816, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617175920, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617176066, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617176218, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617176332, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617176447, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617176571, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617176659, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617176762, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617176884, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617177038, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617177165, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617177295, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617177434, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617177538, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617177622, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617177735, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617177855, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617177946, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617178079, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617178200, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617178264, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617178399, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617178479, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617178552, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617178674, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617178808, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617178959, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617179038, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617179163, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617179290, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617179412, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617179512, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617179600, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617179688, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617179797, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617181020, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617182019, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617183143, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617184210, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617184850, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617185855, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617186638, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617187659, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617188605, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617189356, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617190319, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617191152, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617191937, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617192840, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617193600, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617194909, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617195587, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617196476, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617197394, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617198258, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617199182, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617200106, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617200823, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617201773, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617202581, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617203811, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617204681, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617205393, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617206149, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617206817, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617207568, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617208571, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617209640, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617210701, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617211520, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617212508, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617213711, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617214925, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617215937, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617217010, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617218047, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617219155, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617219562, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617220092, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617220781, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617220941, "dur": 2873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617223815, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617224363, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617224458, "dur": 1827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617226286, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617226641, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617226874, "dur": 2411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617229285, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617229877, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617230118, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617230258, "dur": 1484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617231743, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617232182, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617233410, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617233464, "dur": 2117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617235582, "dur": 681, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617236311, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707617236451, "dur": 2685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617239136, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617239461, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617240350, "dur": 109663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617350018, "dur": 3291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617353309, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617353424, "dur": 3130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617356554, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617356651, "dur": 3167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617359831, "dur": 3342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617363174, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617363247, "dur": 3870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617367147, "dur": 3379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617370527, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617370604, "dur": 4058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617374663, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617374889, "dur": 3784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748707617378725, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617378811, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617378940, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617379059, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617379253, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617379473, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617379737, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617379816, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617380107, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617380181, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748707617380248, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617380457, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617380801, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617380938, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617381044, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748707617381100, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617381245, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617381344, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617381486, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617381629, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707617382554, "dur": 2849176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617158731, "dur": 11187, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617170005, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617170238, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617170354, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617170637, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617170815, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617170953, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617171099, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617171292, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617171445, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617171620, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617171709, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617171904, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617172086, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617172238, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617172367, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617172535, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617172624, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617172776, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617172922, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617173053, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617173155, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617173489, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617173590, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617173715, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617173849, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617174027, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617174172, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617174333, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617174456, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617174523, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617174587, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617174697, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617174797, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617174921, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617175060, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617175147, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617175218, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617175330, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617175483, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617175617, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617175701, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617175867, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617175999, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617176081, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617176174, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617176263, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617176349, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617176460, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617176607, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617176745, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617176847, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617177013, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617177112, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617177214, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617177341, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617177488, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617177550, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617177617, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617177709, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617177828, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617177921, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617178045, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617178210, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617178292, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617178345, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617178461, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617178546, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617178685, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617178816, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617178949, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617179023, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617179168, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617179261, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617179366, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617179469, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617179546, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617179633, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617179725, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617180917, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617181873, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617183031, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617184150, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617184801, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617185687, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617186482, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617187506, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617188524, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617189265, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617190051, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617191032, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617191812, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617192688, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617193456, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617194533, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748707617194614, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617194724, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617195495, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617196339, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617197254, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617198143, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617198970, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617200007, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617200705, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617201493, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617202426, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617203531, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617204535, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617205241, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617206020, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617206698, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617207419, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617208204, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617209426, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617210505, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617211348, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617212319, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617213430, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617214602, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617215730, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617216798, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617217836, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617218844, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617219789, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617219888, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617220086, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617220783, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617221035, "dur": 3879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617224915, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617225474, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617226420, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617228919, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617229045, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_4EC1FE8B3807FE28.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617229184, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617229502, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617229718, "dur": 4860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617234579, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617234818, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_95D2DEE1BA1172D7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617234926, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617235025, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617235292, "dur": 1412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617236705, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617236895, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707617237000, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617237071, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617237501, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617238794, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617238905, "dur": 1423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617240328, "dur": 109643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617349977, "dur": 2410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617352387, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617352538, "dur": 3513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617356051, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617356183, "dur": 4057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617360241, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617360309, "dur": 3373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617363729, "dur": 3133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617366863, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617366925, "dur": 4871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617371797, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617371928, "dur": 2958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617374886, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707617375010, "dur": 2931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617377972, "dur": 4564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748707617382550, "dur": 2849153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617158760, "dur": 11183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617169982, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617170155, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617170310, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F9A822B4784E7167.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617170537, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617170700, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617170894, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617171006, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617171153, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617171332, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617171572, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617171703, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617171905, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617172088, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617172216, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617172351, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617172503, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617172582, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617172754, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617172848, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617173006, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617173099, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617173434, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617173503, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617173643, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617173744, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617173939, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617174030, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617174164, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617174252, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617174459, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617174553, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617174662, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617174768, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617174926, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617175027, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617175138, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617175203, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617175314, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617175447, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617175626, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617175727, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617175836, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617175948, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617176017, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617176097, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617176190, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617176306, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617176377, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617176487, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617176613, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617176740, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617176832, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617176955, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617177088, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617177206, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617177330, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617177431, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617177533, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617177609, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617177687, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617177783, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617177901, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617177990, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617178118, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617178269, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617178432, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617178501, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617178571, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617178727, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617178862, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617179003, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617179110, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617179247, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617180110, "dur": 736, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748707617180847, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617181787, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617182939, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617184064, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617184731, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617185700, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617186503, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617187487, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617188542, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617189293, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617190132, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617191098, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617191881, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617192761, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617193525, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617194770, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617195520, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617196360, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617197284, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617198153, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617198981, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617200026, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617200749, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617201558, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617202494, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617203603, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617204598, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617205302, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617206068, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617206740, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617207480, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617208361, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617209475, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617210561, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617211404, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617212394, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617213517, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617214694, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617215835, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617216890, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617217969, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617219037, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617219488, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617219929, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617220117, "dur": 752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617220870, "dur": 1391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617222262, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617222323, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617223705, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617223807, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617223901, "dur": 1454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617225355, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617225602, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617226334, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617226412, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617227717, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617227838, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617228650, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617228922, "dur": 1978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617230900, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617231001, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_A1CE29DB57A110D1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617231132, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617231234, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617232269, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617232640, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617232761, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cursor.Editor.ref.dll_CD3F41468EC5D7E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617232956, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617233039, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617233145, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617233263, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617233378, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617233622, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617234304, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617234639, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617234837, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617234975, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617235135, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617235237, "dur": 1173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617236411, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617236543, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617237410, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617237777, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617238362, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617238725, "dur": 1523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617240250, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707617240530, "dur": 109461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617349999, "dur": 3860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617353860, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617353941, "dur": 2996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617356938, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617357029, "dur": 4452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617361482, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617361552, "dur": 3955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617365508, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617365613, "dur": 3911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617369525, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617369582, "dur": 2744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617372327, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617372400, "dur": 2755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617375205, "dur": 3210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748707617378415, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617378537, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617378708, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617378959, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617379359, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617379490, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617379593, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617379661, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617379810, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617379944, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748707617380005, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617380108, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617380368, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617380594, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617380781, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748707617380835, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617380942, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748707617380997, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617381157, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617381356, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617381439, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707617381639, "dur": 2061655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707619443336, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748707619443297, "dur": 1725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748707619445548, "dur": 281, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707620225940, "dur": 388, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707619446228, "dur": 780122, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748707620230348, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748707620230341, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748707620230473, "dur": 1100, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748707620231577, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617158759, "dur": 11173, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617169997, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617170111, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617170312, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617170500, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617170592, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617170887, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617170979, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617171147, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617171245, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617171474, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617171612, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617171736, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617171858, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617172044, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617172160, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617172304, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617172418, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617172573, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617172697, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617172871, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617172972, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617173115, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617173407, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617173523, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617173634, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617173735, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617173896, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617174028, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617174119, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617174229, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617174406, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617174527, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617174599, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617174669, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617174733, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617174934, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617175062, "dur": 4333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617179395, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617179525, "dur": 12786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617192312, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617192432, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617192674, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617193445, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617194563, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617194728, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617195506, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617196350, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617197264, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617198145, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617198989, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617200022, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617200740, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617201537, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617202488, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617203573, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617204550, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617205289, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617206060, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617206728, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617207477, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617208334, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617209524, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617210629, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617211445, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617212431, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617213612, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617214793, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617215870, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617216893, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617217954, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617218972, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617219950, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617220141, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617220881, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617221237, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617221312, "dur": 1135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617222448, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617222861, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617222967, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617223031, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617223910, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617224201, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617224565, "dur": 1794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617226360, "dur": 1062, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617227425, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617227590, "dur": 1187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617228777, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617229118, "dur": 1404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617230523, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617230636, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617231644, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617232127, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_8B099D8C97AF393F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617232269, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617232462, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617232580, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_94629FB7B2A83F68.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617232712, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617232786, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617232879, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617232978, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617233128, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617233261, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617233709, "dur": 1854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617235563, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617235798, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617235973, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617236145, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707617236233, "dur": 1161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617237394, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617237461, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617238136, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617238230, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617239117, "dur": 1231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617240348, "dur": 109653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617350008, "dur": 3746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617353755, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617353826, "dur": 3304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617357131, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617357478, "dur": 3812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617361291, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617361348, "dur": 3896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617365245, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617365320, "dur": 3156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617368476, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617368576, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617371124, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617371190, "dur": 3619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617374810, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617374978, "dur": 5081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748707617380059, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617380180, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617380356, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617380449, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748707617380500, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617380882, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617380947, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748707617381093, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617381178, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617381252, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748707617381415, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617381554, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617381606, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707617382221, "dur": 2849452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617158792, "dur": 11186, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617169978, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617170119, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617170273, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617170478, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617170658, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617170867, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617170949, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617171152, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617171250, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617171506, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617171569, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617171711, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617171853, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617172153, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617172246, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617172421, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617172512, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617172679, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617172776, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617172974, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617173066, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617173420, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617173522, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617173660, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617173753, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617173957, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617174059, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617174171, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617174280, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617174422, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617174518, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617174580, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617174695, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617174778, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617174929, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617175068, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617175188, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617175296, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617175419, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617175576, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617175697, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617175818, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617175919, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617175995, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617176058, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617176145, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617176288, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617176386, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617176483, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617176628, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617176748, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617176876, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617176939, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617177060, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617177196, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617177297, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617177399, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617177508, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617177566, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617177629, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617177743, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617177848, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617177950, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617178083, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617178204, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617178338, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617178451, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617178518, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617178638, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617178786, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617178934, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617179028, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617179132, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617179282, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617179416, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617179506, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617179582, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617179642, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617179743, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617180945, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617181864, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617183043, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617184135, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617184790, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617185766, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617186533, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617187564, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617188554, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617189298, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617190190, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617191105, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617191891, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617192798, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617193551, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617194887, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617195553, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617196400, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617197318, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617198226, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617199123, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617200098, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617200797, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617201707, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617202541, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617203699, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617204623, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617205329, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617206097, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617206761, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617207516, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617208453, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617209527, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617210651, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617211465, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617212467, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617213676, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617214893, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617215905, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617216959, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617218014, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617219054, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617219467, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617219641, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617220091, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617220875, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617221451, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617221601, "dur": 1749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707617223351, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617223481, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617223631, "dur": 2925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707617226556, "dur": 919, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617227499, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707617228585, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617228906, "dur": 1728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707617230635, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617230729, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617230836, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617230893, "dur": 2139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707617233032, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617233337, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617233544, "dur": 2033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707617235577, "dur": 497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617236083, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617236141, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617236729, "dur": 2018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707617238748, "dur": 747, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617239507, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617239624, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707617240060, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617240232, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707617240431, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707617240707, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707617240828, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707617242204, "dur": 2199868, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707619443292, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707619444119, "dur": 50, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707619444935, "dur": 287281, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707619734823, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748707619734796, "dur": 1491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748707619736836, "dur": 263, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707620178363, "dur": 579, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707619737287, "dur": 441664, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748707620184976, "dur": 860, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748707620185841, "dur": 45967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617158764, "dur": 11246, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617170020, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617170293, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617170413, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617170623, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617170761, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617170832, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617170998, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617171141, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617171239, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617171352, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617171619, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617171716, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617171849, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617172009, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617172202, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617172311, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617172490, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617172562, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617172740, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617172871, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617173025, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617173131, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617173466, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617173562, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617173686, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617173809, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617173950, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617174050, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617174189, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617174358, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617174514, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617174573, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617174716, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617174842, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617174948, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617175071, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617175163, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617175228, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617175364, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617175529, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617175658, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617175766, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617175832, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617175941, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617176005, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617176083, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617176179, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617176287, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617176393, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617176518, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617176603, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617176671, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617176808, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617176991, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617177104, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617177247, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617177385, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617177543, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617177647, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617177761, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617177866, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617177957, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617178074, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617178212, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617178294, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617178382, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617178445, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617178508, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617178599, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617178749, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617178915, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617179031, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617179154, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617179259, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617179385, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617179488, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617179560, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617179662, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617179735, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617181040, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617182009, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617183105, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617184191, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617184829, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617185840, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617186668, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617187683, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617188654, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617189415, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617190471, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617191237, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617191995, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617192876, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617193656, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617194948, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617195624, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617196497, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617197422, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617198287, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617199244, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617200127, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617200814, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617201755, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617202574, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617203732, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617204657, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617205352, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617206127, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617206792, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617207545, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617208472, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617209532, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617210641, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617211463, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617212463, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617213660, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617214809, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617215872, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617216912, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617217952, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617219002, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617219645, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617219695, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617219929, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617220091, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617220844, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617221313, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617221367, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617222268, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617222545, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617222621, "dur": 1053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617223703, "dur": 1678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617225381, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617225635, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617225689, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617225978, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617226102, "dur": 1254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617227393, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617228086, "dur": 1396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617229482, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617229763, "dur": 4324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617234087, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617234380, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_FE8D1F6CE20A2409.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617234437, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617234524, "dur": 1125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617235649, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617235834, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617235945, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617236014, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617236423, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617236631, "dur": 1080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617237711, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617238006, "dur": 2237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617240244, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707617240398, "dur": 109585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617349990, "dur": 5350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617355341, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617355425, "dur": 3506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617358931, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617359010, "dur": 2462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617361473, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617361616, "dur": 3272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617364889, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617364972, "dur": 3879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617368852, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617369429, "dur": 3466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617372896, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617372976, "dur": 3452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617376429, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707617376510, "dur": 5587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748707617382126, "dur": 2848225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707620230365, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748707620230360, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748707620230452, "dur": 1186, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748707617158773, "dur": 11184, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617169962, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617170087, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617170230, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617170417, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617170596, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3BDCA2C56F5A3F82.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617170802, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617170902, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617171087, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617171237, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617171453, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617171592, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617171741, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617171902, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617172140, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617172221, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617172455, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617172540, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617172707, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617172812, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617172998, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617173095, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617173427, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617173532, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617173661, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617173785, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617173963, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617174065, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617174176, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617174350, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617174478, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617174605, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617174717, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617174891, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617174985, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617175103, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617175175, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_C5A9EDBF39CB7C6A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617175295, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617175394, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617175552, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617175674, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_18DD15F206BB0EF4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617175810, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617175931, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617176023, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617176151, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617176304, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617176407, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617176548, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617176638, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617176749, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617176819, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617176999, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617177099, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617177196, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617177309, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617177403, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617177526, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617177599, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617177672, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617177821, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617177939, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617178039, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617178176, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617178263, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617178334, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617178424, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617178493, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617178561, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617178712, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617178846, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617178963, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617179072, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617179219, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617179309, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617179451, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617179542, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617179626, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617179717, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617179801, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617181012, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617181935, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617183130, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617184203, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617184838, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617185852, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617186636, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617187669, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617188672, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617189428, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617190481, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617191247, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617192000, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617192887, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617193694, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617194962, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617195638, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617196538, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617197468, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617198354, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617199299, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617200190, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617200884, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617201887, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617202657, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617203921, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617204718, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617205424, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617206175, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617206854, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617207591, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617208578, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617209655, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617210707, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617211534, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617212524, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617213742, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617214967, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617215971, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617217136, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617218133, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617219175, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617219573, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617219837, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617219935, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617220160, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617220869, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617221178, "dur": 1181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617222359, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617222833, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617223127, "dur": 1484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617224611, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617224874, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617224944, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617225115, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617225330, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617225427, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617225764, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617225876, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617226047, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617226168, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617228381, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617228524, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617229631, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617229961, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617230159, "dur": 1101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617231290, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617231802, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617231887, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617231998, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617232502, "dur": 2407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617234910, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617235083, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617235366, "dur": 1581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617236948, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617237295, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617237529, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617238568, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617238982, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707617239070, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617239307, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617239617, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617240351, "dur": 109626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617349980, "dur": 2887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617352867, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617352958, "dur": 5201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617358159, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617358240, "dur": 4333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617362574, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617362651, "dur": 3484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617366136, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617366200, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617368647, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617368722, "dur": 4403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617373126, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617373190, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617375618, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617375682, "dur": 5314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748707617381031, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748707617381083, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617381181, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617381354, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617381459, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617381591, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707617382123, "dur": 2802514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707620184665, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748707620184652, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748707620184990, "dur": 689, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748707620185689, "dur": 46034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707620235741, "dur": 948, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1748707616213450, "dur": 718813, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748707616214369, "dur": 136726, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748707616835146, "dur": 3910, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748707616839058, "dur": 93196, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748707616840071, "dur": 66579, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748707616937351, "dur": 965, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748707616937064, "dur": 1411, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748707615919341, "dur": 2950, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707615922305, "dur": 19309, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707615941690, "dur": 148, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748707615941838, "dur": 215, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707615942844, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748707615948198, "dur": 177, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748707615988696, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748707615942059, "dur": 57019, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707615999087, "dur": 13836, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707616012924, "dur": 106, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707616013092, "dur": 1635, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707616014728, "dur": 90, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748707616014867, "dur": 805, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748707615942171, "dur": 56965, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707615999141, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748707615999534, "dur": 184, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748707615999910, "dur": 2295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616002221, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616002621, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616002762, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616002860, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616003017, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616003098, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616003309, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616003391, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616003491, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616003596, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616003762, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616003864, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616004014, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616004129, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616004307, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616004426, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616004618, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616004734, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616004945, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616005064, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616005189, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616005350, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616005433, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616005713, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748707616005804, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616005918, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616006119, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616006586, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616006766, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616007664, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616008167, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748707616008433, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748707616009130, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616009237, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616009342, "dur": 872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616010258, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616010629, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616010705, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616010903, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616010974, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616011066, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616011166, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616011276, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616011334, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748707616012262, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616012360, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748707616012727, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748707616012844, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707615942168, "dur": 56939, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707615999113, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748707615999517, "dur": 197, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748707615999907, "dur": 2338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616002245, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616002327, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616002722, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616002773, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616002927, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616003044, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616003130, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616003217, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616003363, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616003447, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616003580, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616003714, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616003930, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616004023, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616004187, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616004318, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616004513, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616004662, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616004803, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616004901, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616005044, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616005123, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616005324, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616005419, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616005625, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616005725, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616005818, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616006054, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748707616006176, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616006275, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616006361, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748707616006631, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748707616007537, "dur": 959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748707616008536, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748707616009368, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748707616010456, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748707616010641, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748707616010714, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616010797, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748707616011688, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748707616011767, "dur": 835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748707616012644, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616012701, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748707616012829, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707615942173, "dur": 57021, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707615999214, "dur": 2994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616002229, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616002668, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616002788, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616002918, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616003055, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616003141, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616003315, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616003400, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616003493, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616003617, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616003766, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616003898, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616004046, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616004158, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616004337, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616004458, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616004660, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616004749, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616004890, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616005026, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616005167, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616005307, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616005468, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748707616005683, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616005838, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616006017, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748707616006194, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748707616006291, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748707616006695, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748707616006827, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748707616006941, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748707616007316, "dur": 846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748707616008194, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748707616008985, "dur": 3926, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707615942207, "dur": 57037, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707615999254, "dur": 3006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616002262, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616002340, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F9A822B4784E7167.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616002541, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3BDCA2C56F5A3F82.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616002763, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616002929, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616003020, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616003144, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616003297, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616003421, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616003488, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616003629, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616003737, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616003944, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616004076, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616004184, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616004346, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616004480, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616004716, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616004788, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616004962, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616005112, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616005229, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616005398, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616005526, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748707616005704, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616005798, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748707616005959, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616006103, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616006215, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616006294, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748707616006585, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748707616006891, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748707616007277, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748707616008048, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748707616008427, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748707616008491, "dur": 1744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748707616010241, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748707616010518, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748707616010713, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748707616010986, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616011044, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616011128, "dur": 1077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748707616012248, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748707616012748, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616012816, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748707616012875, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707615942174, "dur": 57035, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707615999215, "dur": 2992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616002235, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616002737, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616002830, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616002986, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616003085, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616003294, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616003370, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616003488, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616003603, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616003745, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616003844, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616003972, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616004101, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616004278, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616004417, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616004620, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616004760, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616004897, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616005017, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616005165, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616005269, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616005451, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616005601, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616005680, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616005834, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616005980, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748707616006186, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748707616006279, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748707616006422, "dur": 8154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748707616014577, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707615942206, "dur": 57024, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707615999234, "dur": 2992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616002255, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616002398, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616002517, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616002730, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616002786, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616002941, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616003037, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616003135, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616003281, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616003394, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616003464, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616003648, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616003783, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616003906, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616003990, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616004143, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616004263, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616004442, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616004583, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616004719, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616004800, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616004932, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616005054, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616005187, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616005314, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616005429, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616005554, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748707616005777, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748707616005877, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748707616006394, "dur": 8265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748707616014659, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707615942171, "dur": 56990, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707615999180, "dur": 3062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616002242, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616002378, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_620BC00CDC1CB284.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616002731, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616002912, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616002993, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616003152, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616003306, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616003440, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616003519, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616003669, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616003824, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616003965, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616004117, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616004227, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616004412, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616004521, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616004700, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616004767, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616004908, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616005041, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616005190, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616005339, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616005431, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616005573, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748707616005754, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616005843, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616005988, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748707616006244, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616006329, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748707616006704, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748707616006852, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748707616007592, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_18DD15F206BB0EF4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748707616007678, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748707616008070, "dur": 2246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748707616010483, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748707616010752, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616010824, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748707616012041, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748707616012133, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748707616012699, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748707616012768, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748707616012834, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707615942211, "dur": 57051, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707615999267, "dur": 2998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616002265, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616002364, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616002437, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616002549, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616002794, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616003013, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616003094, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616003282, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616003357, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616003478, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616003588, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616003742, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616003837, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616003982, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616004076, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616004261, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616004359, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616004563, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616004706, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616004845, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616004926, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616005088, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616005179, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616005288, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616005393, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616005557, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616005666, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616005724, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616005786, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748707616006025, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616006192, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616006269, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616006338, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748707616006740, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748707616006963, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748707616007646, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748707616008065, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748707616008130, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748707616008582, "dur": 1745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748707616010386, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748707616010637, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748707616011761, "dur": 1577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748707616019468, "dur": 279, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 74813, "tid": 153858, "ts": 1748707620258558, "dur": 2793, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 74813, "tid": 153858, "ts": 1748707620263561, "dur": 43, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 74813, "tid": 153858, "ts": 1748707620263918, "dur": 20, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 74813, "tid": 153858, "ts": 1748707620261427, "dur": 2126, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 74813, "tid": 153858, "ts": 1748707620263655, "dur": 263, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 74813, "tid": 153858, "ts": 1748707620263969, "dur": 650, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 74813, "tid": 153858, "ts": 1748707620251665, "dur": 14081, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}