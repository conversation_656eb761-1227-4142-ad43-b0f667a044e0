Base path: '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents', plugins path '/Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=1273 file=/ name=Custom/Invisible pass=<Unnamed Pass 0> ppOnly=0 stripLineD=0 buildPlatform=2 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_NEEDS_RENDERPASS_FBFETCH_FALLBACK uKW= dKW=FOG_LINEAR FOG_EXP FOG_EXP2 UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=4294967328 lang=0 type=Vertex platform=metal reqs=1 mask=6 start=1 ok=1 outsize=2113

Cmd: compileSnippet
  insize=1273 file=/ name=Custom/Invisible pass=<Unnamed Pass 0> ppOnly=0 stripLineD=0 buildPlatform=2 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_NEEDS_RENDERPASS_FBFETCH_FALLBACK uKW= dKW=FOG_LINEAR FOG_EXP FOG_EXP2 UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=4294967328 lang=0 type=Fragment platform=metal reqs=1 mask=6 start=1 ok=1 outsize=592

Cmd: compileSnippet
  insize=44189 file=Assets/Kamgam/SkyCloudsURP/Runtime/Shaders/SkyClouds_2D.shader name=SkyClouds_2D pass=Forward ppOnly=0 stripLineD=0 buildPlatform=2 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_NEEDS_RENDERPASS_FBFETCH_FALLBACK uKW=_MAIN_LIGHT_SHADOWS _LIGHT_LAYERS _FORWARD_PLUS dKW=LOD_FADE_CROSSFADE DOTS_INSTANCING_ON _MAIN_LIGHT_SHADOWS_CASCADE _MAIN_LIGHT_SHADOWS_SCREEN _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS LIGHTMAP_SHADOW_MIXING SHADOWS_SHADOWMASK DIRLIGHTMAP_COMBINED LIGHTMAP_ON DYNAMICLIGHTMAP_ON INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _RECEIVE_SHADOWS_OFF UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=4294967328 lang=3 type=Vertex platform=metal reqs=1298411 mask=6 start=202 ok=1 outsize=39585

Cmd: compileSnippet
  insize=44189 file=Assets/Kamgam/SkyCloudsURP/Runtime/Shaders/SkyClouds_2D.shader name=SkyClouds_2D pass=Forward ppOnly=0 stripLineD=0 buildPlatform=2 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_NEEDS_RENDERPASS_FBFETCH_FALLBACK uKW=_MAIN_LIGHT_SHADOWS _LIGHT_LAYERS _FORWARD_PLUS _ADDITIONAL_LIGHT_SHADOWS _REFLECTION_PROBE_BLENDING _REFLECTION_PROBE_BOX_PROJECTION dKW=_SHADOWS_SOFT _DBUFFER_MRT1 _DBUFFER_MRT2 _DBUFFER_MRT3 _LIGHT_COOKIES _WRITE_RENDERING_LAYERS DEBUG_DISPLAY LOD_FADE_CROSSFADE DOTS_INSTANCING_ON _MAIN_LIGHT_SHADOWS_CASCADE _MAIN_LIGHT_SHADOWS_SCREEN _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS LIGHTMAP_SHADOW_MIXING SHADOWS_SHADOWMASK DIRLIGHTMAP_COMBINED LIGHTMAP_ON DYNAMICLIGHTMAP_ON INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _RECEIVE_SHADOWS_OFF UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=4294967328 lang=3 type=Fragment platform=metal reqs=1298411 mask=6 start=202 ok=1 outsize=61337

