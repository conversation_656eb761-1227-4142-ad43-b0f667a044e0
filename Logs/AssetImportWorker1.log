Unity Editor version:    6000.0.39f1 (15ea7ed0b100)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4 (Build 24E248)
Darwin version:          24.4.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/UnityProjects/Soul_game
-logFile
Logs/AssetImportWorker1.log
-srvPort
63493
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/UnityProjects/Soul_game
/Users/<USER>/UnityProjects/Soul_game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8440450112]  Target information:

Player connection [8440450112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 490169303 [EditorId] 490169303 [Version] 1048832 [Id] OSXEditor(0,Georgis-MacBook-Air.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8440450112] Host joined multi-casting on [***********:54997]...
Player connection [8440450112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 18.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.39f1 (15ea7ed0b100)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/UnityProjects/Soul_game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56963
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.001175 seconds.
- Loaded All Assemblies, in  0.333 seconds
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.314 seconds
Domain Reload Profiling: 648ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (140ms)
		LoadAssemblies (111ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (137ms)
			TypeCache.Refresh (136ms)
				TypeCache.ScanAssembly (123ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (315ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (273ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (40ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (48ms)
			ProcessInitializeOnLoadAttributes (112ms)
			ProcessInitializeOnLoadMethodAttributes (69ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.696 seconds
Refreshing native plugins compatible for Editor in 11.73 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.708 seconds
Domain Reload Profiling: 1402ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (522ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (243ms)
			TypeCache.Refresh (182ms)
				TypeCache.ScanAssembly (162ms)
			BuildScriptInfoCaches (46ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (708ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (546ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (395ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.04 seconds
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 6814.
Memory consumption went from 166.9 MB to 163.6 MB.
Total: 10.841458 ms (FindLiveObjects: 0.820750 ms CreateObjectMapping: 0.263166 ms MarkObjects: 7.465500 ms  DeleteObjects: 2.291458 ms)

========================================================================
Received Import Request.
  Time since last request: 140408.197574 seconds.
  path: Assets/Materials/SnakeRiver_Invis.mat
  artifactKey: Guid(3a88fac13707b419f9d7ed8887661640) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/SnakeRiver_Invis.mat using Guid(3a88fac13707b419f9d7ed8887661640) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2be9dafcf28fd0dcced08794d11d601c') in 0.683329375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/BlueMaterial.mat
  artifactKey: Guid(7e0217b88181ee9498c17d872af6753e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/BlueMaterial.mat using Guid(7e0217b88181ee9498c17d872af6753e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f201125133f572277e8d3fa9e254c23') in 0.020909959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.813689 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/Greybox10xMaterial.mat
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/Greybox10xMaterial.mat using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f64675a3ce85b1452569dd8a4893d19c') in 0.01036725 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.046921 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreyMaterial.mat
  artifactKey: Guid(ae791e817e7146241b92f22d5d072488) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreyMaterial.mat using Guid(ae791e817e7146241b92f22d5d072488) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '895874013e45786d326dd84129fcd887') in 0.00974625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 3.278414 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyClouds2D.mat
  artifactKey: Guid(c56646c00920b00419397d90bac5d646) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyClouds2D.mat using Guid(c56646c00920b00419397d90bac5d646) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf921e2791fc4268394b3f9dee37f815') in 7.434243334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1386.054727 seconds.
  path: Assets/Plugins/FMOD/images/SelectedAlt.png
  artifactKey: Guid(8ce9b717b1bc7564cbe35664f2f178a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/SelectedAlt.png using Guid(8ce9b717b1bc7564cbe35664f2f178a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7b4e5b91eeb9b93c8ac415b3020663d') in 0.133414916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000120 seconds.
  path: Assets/Plugins/FMOD/images/StudioIcon.png
  artifactKey: Guid(a4edfa5854cdec34b98b1c55f0562bdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/StudioIcon.png using Guid(a4edfa5854cdec34b98b1c55f0562bdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'efe7fc660ede82937f6d577d85611091') in 0.005808666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Plugins/FMOD/images/SnapshotIcon.png
  artifactKey: Guid(cf2bba5fb8be7e64ca39979f18eb372a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/SnapshotIcon.png using Guid(cf2bba5fb8be7e64ca39979f18eb372a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5bc4ee6958006f7c402fa0ded1b21d3') in 0.005747583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Materials/Sprites/space.png
  artifactKey: Guid(e1eefd00ffcb149368f3956546ad127b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/space.png using Guid(e1eefd00ffcb149368f3956546ad127b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9c0b81270c086426e6a27424737c1846') in 0.009344167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.894677 seconds.
  path: Assets/Plugins/FMOD/images/TransportPlayButtonOff.png
  artifactKey: Guid(29258b1336a580946bc144df00b74ac1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TransportPlayButtonOff.png using Guid(29258b1336a580946bc144df00b74ac1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd7d98722762a4d9630aebce9f75f425d') in 0.008906583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.081981 seconds.
  path: Assets/Plugins/FMOD/images/TransportStopButtonOff.png
  artifactKey: Guid(cafa069c15865d543a07375373f0a18e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TransportStopButtonOff.png using Guid(cafa069c15865d543a07375373f0a18e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '019575269142ea52e3c38afbc1df5fd8') in 0.004259625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.251804 seconds.
  path: Assets/Materials/wattah/wata2.png
  artifactKey: Guid(e8eef33aef7c2449199b509b1d77ba92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/wata2.png using Guid(e8eef33aef7c2449199b509b1d77ba92) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a8679e565d12619f60dbfc59c6784d1d') in 0.007140708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.257529 seconds.
  path: Assets/Materials/wattah/Water_002_NORM.jpg
  artifactKey: Guid(dbe8769b51d894057a6f16e2c12ad501) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/Water_002_NORM.jpg using Guid(dbe8769b51d894057a6f16e2c12ad501) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b0c0754d02a2c3d36bbe1c4aae340c4') in 0.006078125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.464246 seconds.
  path: Assets/Plugins/FMOD/images/Wrench.png
  artifactKey: Guid(507cd805ad331e54cb9e9cab5a9270b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/Wrench.png using Guid(507cd805ad331e54cb9e9cab5a9270b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '827ea9bcfc24ec08e096d051f978800a') in 0.006709875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.844816 seconds.
  path: Assets/Plugins/FMOD/images/PreviewEmitter.png
  artifactKey: Guid(9519043db3741934fa01455c47683e8c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/PreviewEmitter.png using Guid(9519043db3741934fa01455c47683e8c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '027970d47b35a768e6cc7c60158e0d63') in 0.007869458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Materials/Sprites/ParticleSnakeTexture.png
  artifactKey: Guid(39ea767d6fddc4630a53279ecfb37c92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/ParticleSnakeTexture.png using Guid(39ea767d6fddc4630a53279ecfb37c92) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '68948a8a1ccd8540299758bfd9414e94') in 0.0285255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Materials/wattah/noiseTexture.png
  artifactKey: Guid(8b1fd47baf30c46d7856665c07a5c067) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/noiseTexture.png using Guid(8b1fd47baf30c46d7856665c07a5c067) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b9f0f675e9a92f0334a9ecfb4e32b7b') in 0.004587959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Plugins/FMOD/images/LevelMeter.png
  artifactKey: Guid(21e7a3d41a926364a8b9a6704ebe80d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/LevelMeter.png using Guid(21e7a3d41a926364a8b9a6704ebe80d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b5db46116be9c5491edeb1eca7e25e9c') in 0.004391542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.003200 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Textures/Greybox.psd
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Textures/Greybox.psd using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '403560cf7422b3a0dd8bbe3387517904') in 0.006339 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.434302 seconds.
  path: Assets/Plugins/FMOD/images/FolderIconClosed.png
  artifactKey: Guid(70efeb6d97126f843b30b8ed62d18a4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/FolderIconClosed.png using Guid(70efeb6d97126f843b30b8ed62d18a4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '94e8ae28683ee49bfd8ff889326aaa22') in 0.007010666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.017435 seconds.
  path: Assets/Plugins/FMOD/images/FMODLogoBlack.png
  artifactKey: Guid(36e46b3c334e47e41a0b4ff2f26905ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/FMODLogoBlack.png using Guid(36e46b3c334e47e41a0b4ff2f26905ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b2b59042808f0470c2c3025168b3651') in 0.0041195 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.051011 seconds.
  path: Assets/Plugins/FMOD/images/Delete.png
  artifactKey: Guid(196080340db65c44883dd3f599556fb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/Delete.png using Guid(196080340db65c44883dd3f599556fb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83d47dcbf0d3298b21c17bc9769af2a0') in 0.003602458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.026358 seconds.
  path: Assets/Plugins/FMOD/images/CopyIcon.png
  artifactKey: Guid(6e164dcb85fc8ad4b9ab2f1e883862d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/CopyIcon.png using Guid(6e164dcb85fc8ad4b9ab2f1e883862d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '989a3ba85596ce9c284c48d88659ff41') in 0.004179208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.027681 seconds.
  path: Assets/Materials/Sprites/clouds.png
  artifactKey: Guid(4667168974324450aa73c87c33fbd361) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/clouds.png using Guid(4667168974324450aa73c87c33fbd361) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8e6ae29ccb671686239bd8bbc11ec4a4') in 0.004899833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.027012 seconds.
  path: Assets/Materials/Sprites/box_128_fade.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/box_128_fade.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '76c83a3693f0f394ac3a0c6a88c66b7f') in 0.003378208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.079517 seconds.
  path: Assets/Plugins/FMOD/images/Border.png
  artifactKey: Guid(40848578d1961334d820821bec6175a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/Border.png using Guid(40848578d1961334d820821bec6175a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6b87a4ea7dc4cc6a048e62e4c1646668') in 0.008217125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.142168 seconds.
  path: Assets/Plugins/FMOD/images/ArrowIcon.png
  artifactKey: Guid(01c0101f357b9da4ba78b8f58c290f86) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/ArrowIcon.png using Guid(01c0101f357b9da4ba78b8f58c290f86) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0845405b9e868243e8fe9d2135c69034') in 0.0080305 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x32c6d3000 may have been prematurely finalized
- Loaded All Assemblies, in  0.771 seconds
Refreshing native plugins compatible for Editor in 2.53 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.71 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.662 seconds
Domain Reload Profiling: 1436ms
	BeginReloadAssembly (306ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (72ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (406ms)
		LoadAssemblies (289ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (663ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (449ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (326ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.2 MB). Loaded Objects now: 6906.
Memory consumption went from 170.2 MB to 166.0 MB.
Total: 7.653542 ms (FindLiveObjects: 0.513667 ms CreateObjectMapping: 0.190458 ms MarkObjects: 5.263667 ms  DeleteObjects: 1.685083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x32c6d3000 may have been prematurely finalized
- Loaded All Assemblies, in  0.682 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.671 seconds
Domain Reload Profiling: 1355ms
	BeginReloadAssembly (218ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (407ms)
		LoadAssemblies (253ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (671ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (524ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (392ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 8.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.2 MB). Loaded Objects now: 6908.
Memory consumption went from 163.4 MB to 161.2 MB.
Total: 13.654667 ms (FindLiveObjects: 1.188208 ms CreateObjectMapping: 0.379417 ms MarkObjects: 9.517833 ms  DeleteObjects: 2.568417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x30d287000 may have been prematurely finalized
- Loaded All Assemblies, in  0.631 seconds
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.653 seconds
Domain Reload Profiling: 1288ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (378ms)
		LoadAssemblies (230ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (653ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 6910.
Memory consumption went from 163.4 MB to 160.9 MB.
Total: 11.475125 ms (FindLiveObjects: 0.719291 ms CreateObjectMapping: 0.281834 ms MarkObjects: 8.787333 ms  DeleteObjects: 1.686250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6083 unused Assets / (2.9 MB). Loaded Objects now: 6911.
Memory consumption went from 153.5 MB to 150.6 MB.
Total: 26.800167 ms (FindLiveObjects: 0.451917 ms CreateObjectMapping: 0.242083 ms MarkObjects: 24.379750 ms  DeleteObjects: 1.726042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f53b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.666 seconds
Refreshing native plugins compatible for Editor in 5.06 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.719 seconds
Domain Reload Profiling: 1388ms
	BeginReloadAssembly (218ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (389ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (170ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (719ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (560ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (419ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.4 MB). Loaded Objects now: 6913.
Memory consumption went from 163.4 MB to 160.0 MB.
Total: 7.465875 ms (FindLiveObjects: 0.625292 ms CreateObjectMapping: 0.306708 ms MarkObjects: 4.726500 ms  DeleteObjects: 1.806875 ms)

Prepare: number of updated asset objects reloaded= 0
