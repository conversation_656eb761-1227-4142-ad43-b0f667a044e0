Unity Editor version:    6000.0.39f1 (15ea7ed0b100)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4 (Build 24E248)
Darwin version:          24.4.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/UnityProjects/Soul_game
-logFile
Logs/AssetImportWorker0.log
-srvPort
58429
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/UnityProjects/Soul_game
/Users/<USER>/UnityProjects/Soul_game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8440450112]  Target information:

Player connection [8440450112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2127783252 [EditorId] 2127783252 [Version] 1048832 [Id] OSXEditor(0,Georgis-MacBook-Air.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8440450112] Host joined multi-casting on [***********:54997]...
Player connection [8440450112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 19.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.39f1 (15ea7ed0b100)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/UnityProjects/Soul_game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56011
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.000789 seconds.
- Loaded All Assemblies, in  0.400 seconds
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.316 seconds
Domain Reload Profiling: 716ms
	BeginReloadAssembly (128ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (166ms)
		LoadAssemblies (128ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (163ms)
			TypeCache.Refresh (162ms)
				TypeCache.ScanAssembly (148ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (316ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (275ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (34ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (50ms)
			ProcessInitializeOnLoadAttributes (115ms)
			ProcessInitializeOnLoadMethodAttributes (72ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.986 seconds
Refreshing native plugins compatible for Editor in 2.68 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.41 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.225 seconds
Domain Reload Profiling: 2211ms
	BeginReloadAssembly (107ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (808ms)
		LoadAssemblies (253ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (590ms)
			TypeCache.Refresh (442ms)
				TypeCache.ScanAssembly (383ms)
			BuildScriptInfoCaches (126ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1225ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (992ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (262ms)
			ProcessInitializeOnLoadAttributes (637ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.25 seconds
Refreshing native plugins compatible for Editor in 5.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.1 MB). Loaded Objects now: 6814.
Memory consumption went from 166.9 MB to 164.8 MB.
Total: 57.177208 ms (FindLiveObjects: 9.326125 ms CreateObjectMapping: 0.963667 ms MarkObjects: 39.590666 ms  DeleteObjects: 7.295125 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x30ffd3000 may have been prematurely finalized
- Loaded All Assemblies, in  1.217 seconds
Refreshing native plugins compatible for Editor in 5.21 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.94 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.799 seconds
Domain Reload Profiling: 2019ms
	BeginReloadAssembly (624ms)
		ExecutionOrderSort (2ms)
		DisableScriptedObjects (38ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (254ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (520ms)
		LoadAssemblies (482ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (183ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (799ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (594ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (439ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 6829.
Memory consumption went from 157.6 MB to 155.0 MB.
Total: 16.055625 ms (FindLiveObjects: 0.975291 ms CreateObjectMapping: 0.445500 ms MarkObjects: 12.337417 ms  DeleteObjects: 2.295875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x30acc7000 may have been prematurely finalized
- Loaded All Assemblies, in  0.682 seconds
Refreshing native plugins compatible for Editor in 2.63 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.81 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.709 seconds
Domain Reload Profiling: 1394ms
	BeginReloadAssembly (218ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (110ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (396ms)
		LoadAssemblies (254ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (709ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (512ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (368ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.89 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 6831.
Memory consumption went from 155.8 MB to 153.3 MB.
Total: 7.482583 ms (FindLiveObjects: 0.746875 ms CreateObjectMapping: 0.302042 ms MarkObjects: 4.944875 ms  DeleteObjects: 1.487500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x30f387000 may have been prematurely finalized
- Loaded All Assemblies, in  1.240 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.635 seconds
Domain Reload Profiling: 1878ms
	BeginReloadAssembly (580ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (128ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (578ms)
		LoadAssemblies (605ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (237ms)
			TypeCache.Refresh (47ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (636ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (482ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (352ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 6833.
Memory consumption went from 155.9 MB to 152.3 MB.
Total: 8.371209 ms (FindLiveObjects: 0.396500 ms CreateObjectMapping: 0.259667 ms MarkObjects: 5.909333 ms  DeleteObjects: 1.805000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x320acf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.764 seconds
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.47 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.731 seconds
Domain Reload Profiling: 1499ms
	BeginReloadAssembly (224ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (470ms)
		LoadAssemblies (265ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (222ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (732ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (552ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (394ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.8 MB). Loaded Objects now: 6835.
Memory consumption went from 155.9 MB to 153.0 MB.
Total: 8.269958 ms (FindLiveObjects: 0.455000 ms CreateObjectMapping: 0.323542 ms MarkObjects: 6.154500 ms  DeleteObjects: 1.336500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x30ab07000 may have been prematurely finalized
- Loaded All Assemblies, in  1.221 seconds
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.671 seconds
Domain Reload Profiling: 1894ms
	BeginReloadAssembly (419ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (173ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (720ms)
		LoadAssemblies (616ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (241ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (192ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (671ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (497ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (362ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 6837.
Memory consumption went from 155.8 MB to 151.8 MB.
Total: 8.006875 ms (FindLiveObjects: 1.318458 ms CreateObjectMapping: 0.321167 ms MarkObjects: 4.905750 ms  DeleteObjects: 1.461084 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x309833000 may have been prematurely finalized
- Loaded All Assemblies, in  0.720 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.691 seconds
Domain Reload Profiling: 1414ms
	BeginReloadAssembly (246ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (27ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (106ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (409ms)
		LoadAssemblies (235ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (180ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (692ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (525ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (382ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.7 MB). Loaded Objects now: 6839.
Memory consumption went from 155.8 MB to 153.1 MB.
Total: 11.253917 ms (FindLiveObjects: 1.293792 ms CreateObjectMapping: 1.237375 ms MarkObjects: 6.181500 ms  DeleteObjects: 2.540000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 119084.484476 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '45bbbf44ca6fa19466178d6b910aa81d') in 0.560059458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 7.466327 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '009b8f1412230e5e6914b5bbedc426c1') in 0.038445084 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 356.901005 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c75f60a6388ac9d5e63830bbf009a272') in 0.172033584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (2.9 MB). Loaded Objects now: 6994.
Memory consumption went from 157.1 MB to 154.2 MB.
Total: 79.106416 ms (FindLiveObjects: 0.988666 ms CreateObjectMapping: 0.216542 ms MarkObjects: 75.872583 ms  DeleteObjects: 2.027875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x309833000 may have been prematurely finalized
- Loaded All Assemblies, in  1.214 seconds
Refreshing native plugins compatible for Editor in 3.12 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.30 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.897 seconds
Domain Reload Profiling: 2116ms
	BeginReloadAssembly (476ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (51ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (132ms)
	RebuildCommonClasses (50ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (644ms)
		LoadAssemblies (593ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (277ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (217ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (899ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (681ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (124ms)
			ProcessInitializeOnLoadAttributes (483ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.9 MB). Loaded Objects now: 6875.
Memory consumption went from 163.3 MB to 159.4 MB.
Total: 9.264584 ms (FindLiveObjects: 0.441167 ms CreateObjectMapping: 0.364458 ms MarkObjects: 6.663083 ms  DeleteObjects: 1.795125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 426.400326 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a690b174056f13877e9b29aa656cfdf') in 0.390094834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 8.297122 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '08d6f08bced0aaaa00680df9f74c53c8') in 0.014569792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 16.891350 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7147da83f28f827e839cdc5575480b8f') in 0.04527525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 43.698124 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa1171da39f2802112c7e92456dab0be') in 0.03668675 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x309833000 may have been prematurely finalized
- Loaded All Assemblies, in  0.900 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.91 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.871 seconds
Domain Reload Profiling: 1775ms
	BeginReloadAssembly (382ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (152ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (444ms)
		LoadAssemblies (299ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (235ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (204ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (871ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (651ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (477ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.48 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 6897.
Memory consumption went from 167.5 MB to 163.5 MB.
Total: 13.485167 ms (FindLiveObjects: 0.598167 ms CreateObjectMapping: 0.479458 ms MarkObjects: 10.296250 ms  DeleteObjects: 2.110542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 154.223917 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c3d80efa5634110b0e29658e2be96f0a') in 0.485324375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 7.393645 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '865c539b886ca6203b6f993f63c3ee94') in 0.07559725 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  1.388 seconds
Refreshing native plugins compatible for Editor in 5.28 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.761 seconds
Domain Reload Profiling: 2151ms
	BeginReloadAssembly (641ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (124ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (159ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (654ms)
		LoadAssemblies (606ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (243ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (196ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (762ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (554ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (404ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 6919.
Memory consumption went from 171.5 MB to 168.3 MB.
Total: 11.784375 ms (FindLiveObjects: 0.426625 ms CreateObjectMapping: 0.375958 ms MarkObjects: 8.609292 ms  DeleteObjects: 2.371625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  0.890 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.748 seconds
Domain Reload Profiling: 1645ms
	BeginReloadAssembly (276ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (145ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (534ms)
		LoadAssemblies (283ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (313ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (260ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (751ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (565ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (392ms)
			ProcessInitializeOnLoadMethodAttributes (73ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.4 MB). Loaded Objects now: 6921.
Memory consumption went from 171.0 MB to 166.6 MB.
Total: 7.848584 ms (FindLiveObjects: 0.529875 ms CreateObjectMapping: 0.380459 ms MarkObjects: 5.296459 ms  DeleteObjects: 1.640625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 546.611564 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '82617facbf1bca937ce8d0108bd90b4a') in 0.509297333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  1.186 seconds
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.864 seconds
Domain Reload Profiling: 2053ms
	BeginReloadAssembly (445ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (75ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (163ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (611ms)
		LoadAssemblies (475ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (259ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (220ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (865ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (663ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (117ms)
			ProcessInitializeOnLoadAttributes (491ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.9 MB). Loaded Objects now: 6943.
Memory consumption went from 175.5 MB to 172.6 MB.
Total: 26.084125 ms (FindLiveObjects: 2.266666 ms CreateObjectMapping: 1.229500 ms MarkObjects: 20.613917 ms  DeleteObjects: 1.972666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  1.199 seconds
Refreshing native plugins compatible for Editor in 4.25 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.696 seconds
Domain Reload Profiling: 1897ms
	BeginReloadAssembly (502ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (41ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (145ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (619ms)
		LoadAssemblies (531ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (264ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (219ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (696ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.70 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.0 MB). Loaded Objects now: 6945.
Memory consumption went from 175.1 MB to 170.2 MB.
Total: 8.289042 ms (FindLiveObjects: 0.557416 ms CreateObjectMapping: 0.609875 ms MarkObjects: 5.233334 ms  DeleteObjects: 1.887667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  0.804 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.704 seconds
Domain Reload Profiling: 1512ms
	BeginReloadAssembly (289ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (126ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (450ms)
		LoadAssemblies (291ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (222ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (190ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (704ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (516ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (367ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.1 MB). Loaded Objects now: 6947.
Memory consumption went from 175.1 MB to 171.0 MB.
Total: 12.479250 ms (FindLiveObjects: 0.533417 ms CreateObjectMapping: 0.361834 ms MarkObjects: 9.191541 ms  DeleteObjects: 2.391708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3238.882986 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Prefabs/SkyClouds/Cloud 01 (Small).prefab
  artifactKey: Guid(916d4cbb8ac628e42abe9a91de92f642) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Prefabs/SkyClouds/Cloud 01 (Small).prefab using Guid(916d4cbb8ac628e42abe9a91de92f642) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb05b69b0c0854fa9bebe7f1db28ec16') in 0.544726667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  0.817 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.695 seconds
Domain Reload Profiling: 1516ms
	BeginReloadAssembly (315ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (71ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (130ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (434ms)
		LoadAssemblies (231ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (262ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (200ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (696ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (525ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (377ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 6972.
Memory consumption went from 179.7 MB to 176.4 MB.
Total: 8.900666 ms (FindLiveObjects: 0.509375 ms CreateObjectMapping: 0.394458 ms MarkObjects: 6.224334 ms  DeleteObjects: 1.771333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 274.480100 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '409719d5d0e1d41e96f93a497940a65b') in 0.393677666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 10.122119 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd28fe23aad57f750d6f5a6570e4ecfc1') in 0.032295083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 1.699661 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '294a9f2a1029e7399913bf6f30e11b22') in 0.022240291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 11.928485 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '033241aa2322699b24330bb66032c73f') in 0.026051375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  0.795 seconds
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.934 seconds
Domain Reload Profiling: 1732ms
	BeginReloadAssembly (223ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (502ms)
		LoadAssemblies (337ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (211ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (934ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (696ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (540ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.9 MB). Loaded Objects now: 6994.
Memory consumption went from 183.7 MB to 180.8 MB.
Total: 13.973584 ms (FindLiveObjects: 0.484792 ms CreateObjectMapping: 0.853333 ms MarkObjects: 11.096250 ms  DeleteObjects: 1.538084 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  1.375 seconds
Refreshing native plugins compatible for Editor in 3.84 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.42 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.702 seconds
Domain Reload Profiling: 2081ms
	BeginReloadAssembly (635ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (88ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (171ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (666ms)
		LoadAssemblies (582ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (263ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (219ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (520ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (385ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.6 MB). Loaded Objects now: 6996.
Memory consumption went from 183.4 MB to 178.8 MB.
Total: 8.540833 ms (FindLiveObjects: 0.575458 ms CreateObjectMapping: 0.414500 ms MarkObjects: 5.166959 ms  DeleteObjects: 2.383000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  0.707 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.21 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.665 seconds
Domain Reload Profiling: 1375ms
	BeginReloadAssembly (253ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (389ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (666ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (509ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (369ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 4.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 6998.
Memory consumption went from 183.4 MB to 180.0 MB.
Total: 10.203125 ms (FindLiveObjects: 0.944542 ms CreateObjectMapping: 0.595583 ms MarkObjects: 6.514750 ms  DeleteObjects: 2.147458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  1.208 seconds
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.736 seconds
Domain Reload Profiling: 1947ms
	BeginReloadAssembly (446ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (143ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (624ms)
		LoadAssemblies (564ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (242ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (192ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (736ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (527ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (363ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.1 MB). Loaded Objects now: 7000.
Memory consumption went from 183.4 MB to 179.3 MB.
Total: 9.548833 ms (FindLiveObjects: 0.438625 ms CreateObjectMapping: 0.309916 ms MarkObjects: 6.908375 ms  DeleteObjects: 1.891042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17cc07000 may have been prematurely finalized
- Loaded All Assemblies, in  0.970 seconds
Refreshing native plugins compatible for Editor in 2.28 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.69 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.781 seconds
Domain Reload Profiling: 1755ms
	BeginReloadAssembly (350ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (150ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (534ms)
		LoadAssemblies (399ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (258ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (216ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (782ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (570ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (395ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.59 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.7 MB). Loaded Objects now: 7002.
Memory consumption went from 183.4 MB to 179.8 MB.
Total: 15.937959 ms (FindLiveObjects: 1.177833 ms CreateObjectMapping: 1.629167 ms MarkObjects: 11.340458 ms  DeleteObjects: 1.789292 ms)

Prepare: number of updated asset objects reloaded= 0
