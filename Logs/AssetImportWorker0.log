Unity Editor version:    6000.0.39f1 (15ea7ed0b100)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4 (Build 24E248)
Darwin version:          24.4.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/UnityProjects/Soul_game
-logFile
Logs/AssetImportWorker0.log
-srvPort
63493
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/UnityProjects/Soul_game
/Users/<USER>/UnityProjects/Soul_game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8440450112]  Target information:

Player connection [8440450112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1832323524 [EditorId] 1832323524 [Version] 1048832 [Id] OSXEditor(0,Georgis-MacBook-Air.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8440450112] Host joined multi-casting on [***********:54997]...
Player connection [8440450112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 18.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.39f1 (15ea7ed0b100)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/UnityProjects/Soul_game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56961
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.003456 seconds.
- Loaded All Assemblies, in  0.333 seconds
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.315 seconds
Domain Reload Profiling: 649ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (140ms)
		LoadAssemblies (111ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (137ms)
			TypeCache.Refresh (136ms)
				TypeCache.ScanAssembly (123ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (316ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (273ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (47ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (108ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.695 seconds
Refreshing native plugins compatible for Editor in 11.74 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.709 seconds
Domain Reload Profiling: 1401ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (521ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (241ms)
			TypeCache.Refresh (183ms)
				TypeCache.ScanAssembly (160ms)
			BuildScriptInfoCaches (44ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (709ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (547ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (397ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.04 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.5 MB). Loaded Objects now: 6814.
Memory consumption went from 167.1 MB to 163.6 MB.
Total: 9.958541 ms (FindLiveObjects: 1.003791 ms CreateObjectMapping: 0.258584 ms MarkObjects: 6.832416 ms  DeleteObjects: 1.860500 ms)

========================================================================
Received Import Request.
  Time since last request: 140408.196162 seconds.
  path: Assets/Tut/Clouds.mat
  artifactKey: Guid(e5ea6bb4f9e3d45d49f11cccedb533ac) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tut/Clouds.mat using Guid(e5ea6bb4f9e3d45d49f11cccedb533ac) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '27db7b17921fe6d4b8065f79e249ca7e') in 0.697728458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000102 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/BlackMaterial.mat
  artifactKey: Guid(21aa06fceebc0a54fb182c7587193631) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/BlackMaterial.mat using Guid(21aa06fceebc0a54fb182c7587193631) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a6365768e5a206f6fa48e7acf11a543d') in 0.0674185 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.770678 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreenMaterial.mat
  artifactKey: Guid(cf945f6d7077aff4ca37b5c58f80c2e2) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreenMaterial.mat using Guid(cf945f6d7077aff4ca37b5c58f80c2e2) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2c1f11e1d72ecda86bfb8f21abce70a8') in 0.011452333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.046253 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreyboxMaterial.mat
  artifactKey: Guid(********************************) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/GreyboxMaterial.mat using Guid(********************************) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ea87471eee73ef422c1b25a73e0a8869') in 0.009579208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.203398 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/RedMaterial.mat
  artifactKey: Guid(4f41629ff156d6045ba43af08e275039) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/RedMaterial.mat using Guid(4f41629ff156d6045ba43af08e275039) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b6c6e8cb2d2436b6cd416892e60c60c5') in 0.023382083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.030447 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudIntersectionFade.mat
  artifactKey: Guid(60381268557768741b0498ef9caa18d1) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudIntersectionFade.mat using Guid(60381268557768741b0498ef9caa18d1) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9111226587fed950ccfcb2b24c7a049') in 0.010404792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.021300 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/ThirdParty/SkyClouds_StylizedClouds.mat
  artifactKey: Guid(8dd590b390be4264a87b624e0980e669) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/ThirdParty/SkyClouds_StylizedClouds.mat using Guid(8dd590b390be4264a87b624e0980e669) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '45b0319b25369403df84b5c6ce3c9a29') in 0.017357209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000118 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsBig.mat
  artifactKey: Guid(4ff3d050c5199cc49b1ebd390ea0be62) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsBig.mat using Guid(4ff3d050c5199cc49b1ebd390ea0be62) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '586ac1ff7205492d953c40779f383eae') in 0.009010625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 4.240213 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsMid.mat
  artifactKey: Guid(c99f2b6a7d5063f4c8bf7fc0304a0545) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsMid.mat using Guid(c99f2b6a7d5063f4c8bf7fc0304a0545) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '948b25ff6306fb54279ae1bb0d1d74b7') in 0.011791875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/_Test/SkyCloudsPlane/SkyCloudsPlane.mat
  artifactKey: Guid(d49e9a268b12eeb48b392db14a6fb47b) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Test/SkyCloudsPlane/SkyCloudsPlane.mat using Guid(d49e9a268b12eeb48b392db14a6fb47b) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f40902ac3ff40e5c577bda7903fb183a') in 0.096011834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_0.4.mat
  artifactKey: Guid(ea7e4fbd8c8f6b045b703a668591c329) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_0.4.mat using Guid(ea7e4fbd8c8f6b045b703a668591c329) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9e9c21d8dcb3a4f577cd985ad85405f3') in 0.008959584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_2.mat
  artifactKey: Guid(7b9d195a8c7b72d4cbcb1ce6b6107d8a) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_2.mat using Guid(7b9d195a8c7b72d4cbcb1ce6b6107d8a) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1be2871224d277085f22b1a4e4c8b33') in 0.007466917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_ControlledWind.mat
  artifactKey: Guid(32b06ee158459e3449c919090eb79363) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_ControlledWind.mat using Guid(32b06ee158459e3449c919090eb79363) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ace94f2d1483b565d7f4248e009ed1ae') in 0.0075395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_0.8.mat
  artifactKey: Guid(0f22d337e60da8149bb0ffc64e0c7dde) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_0.8.mat using Guid(0f22d337e60da8149bb0ffc64e0c7dde) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '94337e801268cdc7af29f6263c5d26a6') in 0.010550458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Flat.mat
  artifactKey: Guid(70771b08ad6ef614dafbb010f1502713) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Flat.mat using Guid(70771b08ad6ef614dafbb010f1502713) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db041f1a8eb3fe71c79d2baecd5f9cf5') in 0.009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_NoiseColor.mat
  artifactKey: Guid(2889243cbb289b24c8f6ccbd585555f6) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_NoiseColor.mat using Guid(2889243cbb289b24c8f6ccbd585555f6) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7bb6cb85e1bac003261655b58b851c03') in 0.008254417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Colorful.mat
  artifactKey: Guid(6b5502d376d3f274abc6927d5ab28d4d) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Colorful.mat using Guid(6b5502d376d3f274abc6927d5ab28d4d) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3670ab35f5eb9102776a24b9bcbb44f5') in 0.009790458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_0.5.mat
  artifactKey: Guid(129ad2f4fcb647649ad7876d8f56f497) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_0.5.mat using Guid(129ad2f4fcb647649ad7876d8f56f497) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f56dc0771616a276bd3cc05e1fc5d3e2') in 0.008086541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsSmall.mat
  artifactKey: Guid(5453f3c4cad0b444ab476747ba6a82b2) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/SkyCloudsSmall.mat using Guid(5453f3c4cad0b444ab476747ba6a82b2) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '84cb92e952454a877389c1565003ed79') in 0.008332666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall.mat
  artifactKey: Guid(87047a5db84f44d479dbb94da4c1d217) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall.mat using Guid(87047a5db84f44d479dbb94da4c1d217) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3c760dee1c22160f4d4595cbb907763c') in 0.008625916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default.mat
  artifactKey: Guid(fcddd1cc1df5dfc47bc64c8211c3edae) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default.mat using Guid(fcddd1cc1df5dfc47bc64c8211c3edae) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c5f1cedf8a282dbe4194ed80ffcdc0dd') in 0.008205458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_1.2.mat
  artifactKey: Guid(a55b2ff73341ebf4fa9e17b9af723a7e) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Default_fade_1.2.mat using Guid(a55b2ff73341ebf4fa9e17b9af723a7e) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '70b79ced662554cb9536720d9d524847') in 0.008151083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.121964 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_2.mat
  artifactKey: Guid(df8228e2063f02748ab26a17d1a1ed42) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_2.mat using Guid(df8228e2063f02748ab26a17d1a1ed42) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2734070324a144a83734374b7c181d13') in 0.012682125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_4.mat
  artifactKey: Guid(515eacba2a3d8324ab0c03e6ff144fb6) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_Scale_4.mat using Guid(515eacba2a3d8324ab0c03e6ff144fb6) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f464607057fa43be6d0cb4341f3d356c') in 0.00802875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_WindyGrey.mat
  artifactKey: Guid(b5732e4e24da22a46a5859f65c4915e9) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_WindyGrey.mat using Guid(b5732e4e24da22a46a5859f65c4915e9) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd42009af25c605ee78529acbe8ee1def') in 0.007677625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_SemiFlat.mat
  artifactKey: Guid(f3ca28c89f08c5241b7bdef619817aa8) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Examples/Materials/StyleDemos/SkyCloudsSmall_SemiFlat.mat using Guid(f3ca28c89f08c5241b7bdef619817aa8) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1a5e3c1f86c79b499164fb996d961c02') in 0.009429666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.002518 seconds.
  path: Assets/Materials/SnakeRiver_Tail.mat
  artifactKey: Guid(c6a3bbfd055b743a8acd36820bb87725) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/SnakeRiver_Tail.mat using Guid(c6a3bbfd055b743a8acd36820bb87725) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc048a629b2ec4a009017764c08085c7') in 0.015630291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.362632 seconds.
  path: Assets/Materials/SoulCreatureGiant.mat
  artifactKey: Guid(9d2a0b86b9b4442a4bbfecfcb46449e3) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/SoulCreatureGiant.mat using Guid(9d2a0b86b9b4442a4bbfecfcb46449e3) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7ccf2a759dc887c74c74141d87ad123c') in 0.01136 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1351.060607 seconds.
  path: Assets/Materials/Lightning.mat
  artifactKey: Guid(fb1538b73536c4723af4ca6432fc2a3e) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Lightning.mat using Guid(fb1538b73536c4723af4ca6432fc2a3e) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '32a73552a37aa258b80d6ab4d60422a5') in 0.082924583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 36.791107 seconds.
  path: Assets/Plugins/FMOD/images/SearchIconBlack.png
  artifactKey: Guid(99471facfde9fb84dbe4a81ad570ebce) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/SearchIconBlack.png using Guid(99471facfde9fb84dbe4a81ad570ebce) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8e2fc879457dd181489e170bf807cc70') in 0.121216 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000137 seconds.
  path: Assets/Plugins/FMOD/images/TickGreen.png
  artifactKey: Guid(27533226416c4f549b9bec9c024100f7) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TickGreen.png using Guid(27533226416c4f549b9bec9c024100f7) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '16d5de7a6a3ecead0e74fc11618a92d1') in 0.0043975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Materials/Sprites/SoulCreatureGiant.png
  artifactKey: Guid(650c90e0bcc404e52ac828df436d0143) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/SoulCreatureGiant.png using Guid(650c90e0bcc404e52ac828df436d0143) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a1bcce7c54ad07ff2be4344ab9560efa') in 0.028019708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000101 seconds.
  path: Assets/Materials/Sprites/splash.png
  artifactKey: Guid(70e249635be5348d88a4d93785761515) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/splash.png using Guid(70e249635be5348d88a4d93785761515) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0b37e819d245e5911ab133ae9f164218') in 0.010065291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.871592 seconds.
  path: Assets/Plugins/FMOD/images/TransportOpen.png
  artifactKey: Guid(b0fb832e401d1514a9611735d8d340b1) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TransportOpen.png using Guid(b0fb832e401d1514a9611735d8d340b1) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f222eb2c11323fdbd857d02b7e2aa4b0') in 0.0091765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.081037 seconds.
  path: Assets/Plugins/FMOD/images/TransportPlayButtonOn.png
  artifactKey: Guid(2d777c9a14189d241aea1afeeeff448c) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TransportPlayButtonOn.png using Guid(2d777c9a14189d241aea1afeeeff448c) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd6b10e03cfc2ff53d8629a0e7ba8cb20') in 0.004809166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.250950 seconds.
  path: Assets/Plugins/FMOD/images/TransportStopButtonOn.png
  artifactKey: Guid(eab53cb0959d1244aadeacf8b76c755c) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/TransportStopButtonOn.png using Guid(eab53cb0959d1244aadeacf8b76c755c) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1884bbce2251bcd2e1a405b323d2b0a8') in 0.006451667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.256226 seconds.
  path: Assets/Materials/wattah/water 0399normal.jpg
  artifactKey: Guid(c99fa1ffc45624d60984629801fefed3) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/water 0399normal.jpg using Guid(c99fa1ffc45624d60984629801fefed3) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '427525949462d45ad1391ea6e296f443') in 0.007286416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.462711 seconds.
  path: Assets/Materials/wattah/wawawa.png
  artifactKey: Guid(989c2c83f2fb64b64a706199aa17842d) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/wawawa.png using Guid(989c2c83f2fb64b64a706199aa17842d) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7c0fd2b6eb838edff68a81f029271786') in 0.007925167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.843707 seconds.
  path: Assets/Plugins/FMOD/images/Preview.png
  artifactKey: Guid(0793eda432fc5df4ab1291e6baacd771) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/Preview.png using Guid(0793eda432fc5df4ab1291e6baacd771) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '37bee550857e81a72f103ae296128ddb') in 0.00992275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Plugins/FMOD/images/NotFound.png
  artifactKey: Guid(1138ab068176f29499337d7a73dfecd9) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/NotFound.png using Guid(1138ab068176f29499337d7a73dfecd9) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '14269d94c60adb7a1c4df7a66c508a41') in 0.003165584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.013157 seconds.
  path: Assets/Plugins/FMOD/images/LevelMeterOff.png
  artifactKey: Guid(48dc5470d93f669419f294fcd33f7b7c) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/LevelMeterOff.png using Guid(48dc5470d93f669419f294fcd33f7b7c) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0bb1c1d4e84a0a6fe9f7fae3c6be573f') in 0.004686916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Plugins/FMOD/images/LabeledParameterIcon.png
  artifactKey: Guid(b4d696e5c0be6f44bb2f02aa41320656) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/LabeledParameterIcon.png using Guid(b4d696e5c0be6f44bb2f02aa41320656) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00b0aeb6a7c7be69fc0d48691fffb6b8') in 0.005108625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.009089 seconds.
  path: Assets/Plugins/FMOD/images/FolderIconOpen.png
  artifactKey: Guid(d2b54e4f7f80b9448a41d3c5985f5672) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/FolderIconOpen.png using Guid(d2b54e4f7f80b9448a41d3c5985f5672) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1e3ddc215434a51b202e57deed6b8f91') in 0.006359208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.433581 seconds.
  path: Assets/Plugins/FMOD/images/FMODLogoWhite.png
  artifactKey: Guid(8fd8ccb8d7e81d943b28ea7975c7185d) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/FMODLogoWhite.png using Guid(8fd8ccb8d7e81d943b28ea7975c7185d) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eff4c72d6fbb53b34c9a7d6ab814c3d6') in 0.006672917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.016705 seconds.
  path: Assets/Plugins/FMOD/images/EventIcon.png
  artifactKey: Guid(a602f206f9cb31f439c79a2fe23687c5) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/EventIcon.png using Guid(a602f206f9cb31f439c79a2fe23687c5) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d532c97e53015a40463f228cf6f688d') in 0.004108375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.015527 seconds.
  path: Assets/Plugins/FMOD/images/DiscreteParameterIcon.png
  artifactKey: Guid(509563e7079a6ed4cbf3b3240327e702) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/DiscreteParameterIcon.png using Guid(509563e7079a6ed4cbf3b3240327e702) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '93299fc8c9e7a5fae08892252296a9ad') in 0.005090875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.013212 seconds.
  path: Assets/Plugins/FMOD/images/CrossYellow.png
  artifactKey: Guid(348d2265b48c67342a4db2a7062813fa) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/CrossYellow.png using Guid(348d2265b48c67342a4db2a7062813fa) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2c52090b16babdfcff4135957dd578d4') in 0.003621292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.025230 seconds.
  path: Assets/Plugins/FMOD/images/ContinuousParameterIcon.png
  artifactKey: Guid(b92803770616fc747bc3c40ffaec0a42) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/ContinuousParameterIcon.png using Guid(b92803770616fc747bc3c40ffaec0a42) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf07c63bc2305db4e2c6d83fc5994820') in 0.004890291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.026547 seconds.
  path: Assets/Plugins/FMOD/images/BrowserIcon.png
  artifactKey: Guid(c783b763d12874147876e070661b66ab) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/BrowserIcon.png using Guid(c783b763d12874147876e070661b66ab) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e2ce06f22a6668d189c7a7192bd1e9ab') in 0.00505525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.026007 seconds.
  path: Assets/Materials/Sprites/box_128.png
  artifactKey: Guid(********************************) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Sprites/box_128.png using Guid(********************************) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04d3ac4e62430873c3aa1df47a87615c') in 0.********* seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.078687 seconds.
  path: Assets/Plugins/FMOD/images/BankIcon.png
  artifactKey: Guid(a7e06068a7215854a84bf5ed8280ed15) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/BankIcon.png using Guid(a7e06068a7215854a84bf5ed8280ed15) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2fa6baf45da87344f0179b77ee181bdd') in 0.********* seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.141773 seconds.
  path: Assets/Plugins/FMOD/images/AddIcon.png
  artifactKey: Guid(3300e81f02e64924eb7cb7782713b126) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/FMOD/images/AddIcon.png using Guid(3300e81f02e64924eb7cb7782713b126) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'afc1d2cc2b92c8ebfbd8278a9ab7afcf') in 0.******** seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.457655 seconds.
  path: Assets/Materials/wattah/2dwater.png
  artifactKey: Guid(8f70e8844a3c04e50ae9ce8c5bf365c0) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/wattah/2dwater.png using Guid(8f70e8844a3c04e50ae9ce8c5bf365c0) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '020b23f9540835e1685311cbbd6fa8fe') in 0.010431125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17ec87000 may have been prematurely finalized
- Loaded All Assemblies, in  0.772 seconds
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.657 seconds
Domain Reload Profiling: 1432ms
	BeginReloadAssembly (307ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (73ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (106ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (405ms)
		LoadAssemblies (288ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (657ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (449ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.2 MB). Loaded Objects now: 6907.
Memory consumption went from 170.2 MB to 166.0 MB.
Total: 7.638041 ms (FindLiveObjects: 0.479417 ms CreateObjectMapping: 0.208917 ms MarkObjects: 5.223083 ms  DeleteObjects: 1.726375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17dd13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.684 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.687 seconds
Domain Reload Profiling: 1373ms
	BeginReloadAssembly (217ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (409ms)
		LoadAssemblies (261ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (687ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (544ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (407ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 6.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 6909.
Memory consumption went from 163.6 MB to 161.0 MB.
Total: 13.405709 ms (FindLiveObjects: 1.123958 ms CreateObjectMapping: 0.415792 ms MarkObjects: 9.050875 ms  DeleteObjects: 2.813667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17dd13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.624 seconds
Refreshing native plugins compatible for Editor in 3.18 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.643 seconds
Domain Reload Profiling: 1269ms
	BeginReloadAssembly (202ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (221ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (643ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (485ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.5 MB). Loaded Objects now: 6911.
Memory consumption went from 163.5 MB to 161.0 MB.
Total: 12.586958 ms (FindLiveObjects: 0.801625 ms CreateObjectMapping: 2.011458 ms MarkObjects: 7.701334 ms  DeleteObjects: 2.071250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6083 unused Assets / (2.8 MB). Loaded Objects now: 6912.
Memory consumption went from 153.7 MB to 150.8 MB.
Total: 26.807666 ms (FindLiveObjects: 0.466667 ms CreateObjectMapping: 0.257666 ms MarkObjects: 24.358500 ms  DeleteObjects: 1.723709 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17dd13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.685 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.710 seconds
Domain Reload Profiling: 1398ms
	BeginReloadAssembly (237ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (388ms)
		LoadAssemblies (303ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (131ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (711ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (558ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (422ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.1 MB). Loaded Objects now: 6914.
Memory consumption went from 163.5 MB to 160.4 MB.
Total: 7.617167 ms (FindLiveObjects: 0.703834 ms CreateObjectMapping: 0.328417 ms MarkObjects: 4.933250 ms  DeleteObjects: 1.651167 ms)

Prepare: number of updated asset objects reloaded= 0
